{{.Brochure}}
<script
  src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"
  integrity="sha512-v2CJ7UaYy4JwqLDIrZUI/4hqeoQieOmAZNXBeQyjo21dadnwR+8ZaIJVT8EE2iyI61OV8e6M8PP2/4hpQINQ/g=="
  crossorigin="anonymous"
  referrerpolicy="no-referrer"
></script>
<script>
    $(document).ready(function () {
      var v = {{.Json}};
      !function(f,b,e,v,n,t,s)
  {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
  n.callMethod.apply(n,arguments):n.queue.push(arguments)};
  if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
  n.queue=[];t=b.createElement(e);t.async=!0;
  t.src=v;s=b.getElementsByTagName(e)[0];
  s.parentNode.insertBefore(t,s)}(window, document,'script',
  'https://connect.facebook.net/en_US/fbevents.js');
!function (w, d, t) {
w.TiktokAnalyticsObject=t;var ttq=w[t]=w[t]||[];ttq.methods=["page","track","identify","instances","debug","on","off","once","ready","alias","group","enableCookie","disableCookie"],ttq.setAndDefer=function(t,e){t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}};for(var i=0;i<ttq.methods.length;i++)ttq.setAndDefer(ttq,ttq.methods[i]);ttq.instance=function(t){for(var e=ttq._i[t]||[],n=0;n<ttq.methods.length;n++
)ttq.setAndDefer(e,ttq.methods[n]);return e},ttq.load=function(e,n){var i="https://analytics.tiktok.com/i18n/pixel/events.js";ttq._i=ttq._i||{},ttq._i[e]=[],ttq._i[e]._u=i,ttq._t=ttq._t||{},ttq._t[e]=+new Date,ttq._o=ttq._o||{},ttq._o[e]=n||{};n=document.createElement("script");n.type="text/javascript",n.async=!0,n.src=i+"?sdkid="+e+"&lib="+t;e=document.getElementsByTagName("script")[0];e.parentNode.insertBefore(n,e)};
}(window, document, 'ttq');
var gass=v,url=window.location.href;url=(url=url.replace("https://www.","")).replace("http://www.",""),gass.domain=url.replace("http://","").replace("https://","").split(/[/?#]/)[0],gass.send_pixel_fb=0,gass.send_pixel_tt=0,gass.param_get={};for(var parts=window.location.search.substr(1).split("&"),i=0;i<parts.length;i++){var temp=parts[i].split("=");gass.param_get[decodeURIComponent(temp[0])]=decodeURIComponent(temp[1])}function getCookie(t){const e=`; ${document.cookie}`.split(`; ${t}=`);if(2===e.length)return e.pop().split(";").shift()}async function post_visit(t,e,i){t.fbp=getCookie("_fbp"),t.fbc=getCookie("_fbc");let a=new FormData;a=appendFormdata(a,t);try{const e=await request_post(t.api_endpoint_url,a);void 0!==t.connectors&&Object.keys(t.connectors).forEach((e=>{const i=t.connectors[e];if("facebook"===i.type){if(fbq&&0===t.send_pixel_fb){t.send_pixel_fb=1,t.fbq=fbq;const e=i.data.pixel_id;e&&(t.fbq("init",e,{external_id:t.visitor_id}),t.fbq("track","ViewContent",{},{eventID:"ViewContent-"+t.visitor_id}))}}else"tiktok"===i.type&&ttq&&0===t.send_pixel_tt&&(t.send_pixel_tt=1,t.ttq=ttq,t.ttq.load(i.data.pixel_id),t.ttq.page(),t.ttq.track("ViewContent",{user:[{external_id:i.data.visitor_id_hash}],event_id:"ViewContent-"+t.visitor_id}))})),void 0!==i&&i(e)}catch(t){void 0!==i&&i({code:0,msg:"Request failed"})}}function appendFormdata(t,e){e.ip=getCookie("ip_gass"),t.append("page_url",window.location),t.append("domain",e.domain),void 0!==e.ip&&t.append("ip",e.ip),void 0!==typeof e.visitor_id||null!==typeof e.visitor_id?t.append("visitor_id",e.visitor_id):(e.visitor_id=getCookie("visitor_id"),void 0===typeof e.visitor_id&&null===typeof e.visitor_id||t.append("visitor_id",e.visitor_id)),void 0===e.key&&null===e.key||t.append("project_key",e.project_key),void 0!==e.fbc&&void 0!==e.fbc&&null!==e.fbc&&t.append("fbc",e.fbc),void 0!==e.fbp&&void 0!==e.fbp&&null!==e.fbp&&t.append("fbp",e.fbp),void 0!==e._ttp&&void 0!==e._ttp&&null!==e._ttp&&t.append("_ttp",e._ttp),null!==e.ref&&t.append("ref",e.ref);var i=getCookie("client_id");return void 0!==i&&t.append("clientId",i),e.param_get&&Object.keys(e.param_get).forEach((i=>{""!=i&&null!=e.param_get[i]&&t.append(i,e.param_get[i])})),t}async function request_post(t,e){try{const i=await fetch(t,{method:"POST",body:e});if(i.ok){return await i.json()}return{code:0,msg:"Request failed"}}catch(t){return{code:0,msg:"Request Error"}}}async function request_get(t){try{const e=await fetch(t);if(e.ok){return{code:1,msg:await e.text()}}return{code:0,msg:"Request failed"}}catch(t){return{code:0,msg:"Request Error"}}}void 0!==document.referrer&&(gass.ref=document.referrer),window.addEventListener("load",(async function(){try{const t=await request_get("https://ip.gass.co.id/");if(1===t.code){gass.ip=t.gass;const e=1,i="ip_gass",a=t.msg;let s=new Date;s.setTime(s.getTime()+24*e*60*60*1e3);const o="expires="+s.toUTCString();document.cookie=i+"="+a+"; "+o+"; path=/; SameSite=None; Secure"}}catch(t){}gass.timer=null,gass.timer1=null,gass.timer2=null,window.clearInterval(gass.timer),window.clearInterval(gass.timer1),window.clearInterval(gass.timer2);try{await post_visit(gass,"v_update")}catch(t){}const t=6e4,e=2e3;let i=0;gass.timer=window.setInterval((async function(){if(i+=e,gass.fbp=getCookie("_fbp"),void 0!==gass.fbp){try{await post_visit(gass,"v_update")}catch(t){}window.clearInterval(gass.timer)}i>=t&&window.clearInterval(gass.timer)}),e),i=0,gass.timer1=window.setInterval((async function(){if(i+=e,gass.fbc=getCookie("_fbc"),void 0!==gass.fbc){try{await post_visit(gass,"v_update")}catch(t){}window.clearInterval(gass.timer1)}i>=t&&window.clearInterval(gass.timer1)}),e),i=0,gass.timer2=window.setInterval((async function(){i+=e;const a=getCookie("_ttp");if(void 0!==a){gass._ttp=a;try{await post_visit(gass,"v_update")}catch(t){}window.clearInterval(gass.timer2)}i>=t&&window.clearInterval(gass.timer2)}),e)}));
    });
</script>
