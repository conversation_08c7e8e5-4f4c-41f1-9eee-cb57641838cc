<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Autocomplete Select</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-rbsA2VBKQhggwzxH7pPCaAqO46MgnOM80zW1RWuH61DGLwZJEdK2Kadq2F9CUG65" crossorigin="anonymous">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    .card-content {
      border: 1px solid #d7d7d7;
      border-radius: 15px;
      padding: 1.4em;
    }
    body {
      font-family: 'Inter', sans-serif;
    }
  </style>
</head>
<body>
  <div class="container d-flex justify-content-center mb-5">
    <div class="col-12 text-center">
      <img src="/{{.QRCode}}" class="mx-auto">
      <div class="text-center">
        <p style="font-size: 14;color: #314A9F;">{{ .Code }}</p>
      </div>
    </div>
  </div>
  <div class="d-flex justify-content-center mb-5">
    <div class="col-md-6">
      <form method="get" style="width: 100%; text-align: center;">
        <label for="search" style="font-size: 18px;">Store Locator</label>
        <div style="position: relative; width: 100%; display: inline-block;">
          <input type="hidden" name="brochure_id" value="{{ .BrochureID }}">
          <img src="/files/search.png" alt="Search Icon" style="position: absolute; left: 10px; top: 50%; transform: translateY(-50%);">
          <input type="text" id="search" placeholder="Click Here to Search" autocomplete="off" style="width: 100%; border-radius: 15px; height: 3em; padding-left: 40px; box-shadow: none; background: transparent; border: 1px solid; border-color: #d3d3d3;">
        </div>
      </form>
    </div>
  </div>
    
  <div class="container-xxl p-5" style="border: 1px solid #d7d7d7;border-radius: 15px;padding: 1.4em;">
    <div class="row" id="storeLocRealtime">
      {{ range $key, $value := .Stores }}
        <div class="col-md-4 col-sm-12">
          <div class="card-content" style="box-shadow: -3px 6px 8px 0 rgba(0, 0, 0, 0.2);">
            <div class="row">
              <div class="col-10">
                <h2 class="location-title">{{ $value.Name }}</h2>
              </div>
              <div class="col-2">
                  <a href="https://maps.google.com/maps?q={{ $value.Latitude }},{{ $value.Longitude }}&z=17&hl=en" target="_blank">
                    <img src="/files/traffic-sign.png" alt="Clock icon representing maps">
                  </a>
              </div>
            </div>
            <div class="description-block">
              <div class="row">
                <div class="col-1">
                  <img src="/files/map.png" alt="Map image for location">
                </div>
                <div class="col-11 ps-0">
                  <p class="description" style="color: #536471;">{{ $value.Location }}</p>
                </div>
              </div>
              <div class="row">
                <div class="col-1">
                  <img src="/files/clock.png" alt="Clock icon representing hours">
                </div>
                <div class="col-11 ps-0">
                  {{ if ne (len $value.OperationalDays) 0 }}
                  {{ range $key2, $value2 := $value.OperationalDays }}
                  <p class="description" style="color: #536471;">Today at {{ $value2.OpenHours }} - {{ $value2.CloseHours }}</p>
                  {{ end }}
                  {{ else }}
                  <p class="description" style="color: #536471;">Closed</p>
                  {{ end }}
                </div>
              </div>
              <div class="row">
                <div class="col-8 text-end">
                  <img style="display: none;" src="/files/distance.png" alt="Clock icon representing hours">
                </div>
                <div class="col-4 ps-0">
                  <p class="description" style="color: #536471;"></p>
                </div>
              </div>
            </div>

            <!-- Hours (left empty for dynamic content) -->
            <p class="hours"></p>
          </div>
        </div>
      {{ end }}
    </div>
  </div>
   <script>
    document.addEventListener("DOMContentLoaded", () => {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const lat = position.coords.latitude;
            const long = position.coords.longitude;
            
            const searchInput = document.getElementById("search");
            const dropdown = document.getElementById("dropdown");
            const query = searchInput.value;
                  const brochureIdInput = document.querySelector('input[name="brochure_id"]');
                  const brochureId = brochureIdInput.value;
                  fetch(`/get-order-store?q=${encodeURIComponent(query)}&lat=${encodeURIComponent(lat)}&long=${encodeURIComponent(long)}&brochure_id=${encodeURIComponent(brochureId)}`)
                  .then(response => response.json())
                  .then(data => {
                      const container = document.getElementById("storeLocRealtime");
                      container.innerHTML = "";
                      if (data.length > 0) {
                          data.forEach(conn => {
                            
                              const cardContent = document.createElement("div");
                              cardContent.classList.add("card-content");
                              cardContent.style.boxShadow = "-3px 6px 8px 0 rgba(0, 0, 0, 0.2)";
                              
                              const row = document.createElement("div");
                              row.classList.add("row");
                              
                              const col10 = document.createElement("div");
                              col10.classList.add("col-10");
                              
                              const locationTitle = document.createElement("h2");
                              locationTitle.classList.add("location-title");
                              locationTitle.innerText = conn.Name;
                              
                              col10.appendChild(locationTitle);
                              row.appendChild(col10);
                              const col2map = document.createElement("div");
                              col2map.classList.add("col-2");
                              const a = document.createElement("a");
                              a.href = `https://maps.google.com/maps?q=${conn.Latitude},${conn.Longitude}&z=17&hl=en`;
                              a.target = "_blank";
                              const trafficSign = document.createElement("img");
                              trafficSign.src = "/files/traffic-sign.png";
                              trafficSign.alt = "Clock icon representing maps";
                              a.appendChild(trafficSign);
                              col2map.appendChild(a);
                              row.appendChild(col2map);
                              cardContent.appendChild(row);
                              
                              const descriptionBlock = document.createElement("div");
                              descriptionBlock.classList.add("description-block");
                              
                              const row1 = document.createElement("div");
                              row1.classList.add("row");
                              
                              const col1 = document.createElement("div");
                              col1.classList.add("col-1");
                              
                              const mapImg = document.createElement("img");
                              mapImg.src = "/files/map.png";
                              mapImg.alt = "Map image for location";
                              
                              col1.appendChild(mapImg);
                              
                              const col11 = document.createElement("div");
                              col11.classList.add("col-11", "ps-0");
                              
                              const locationDesc = document.createElement("p");
                              locationDesc.classList.add("description");
                              locationDesc.style.color = "#536471";
                              locationDesc.innerText = conn.Location;
                              
                              col11.appendChild(locationDesc);
                              row1.appendChild(col1);
                              row1.appendChild(col11);
                              descriptionBlock.appendChild(row1);
                              
                              const row2 = document.createElement("div");
                              row2.classList.add("row");
                              
                              const col2 = document.createElement("div");
                              col2.classList.add("col-1");
                              
                              const clockImg = document.createElement("img");
                              clockImg.src = "/files/clock.png";
                              clockImg.alt = "Clock icon representing hours";
                              
                              col2.appendChild(clockImg);
                              
                              const col22 = document.createElement("div");
                              col22.classList.add("col-11", "ps-0");
                              
                              const hoursDesc = document.createElement("p");
                              hoursDesc.classList.add("description");
                              hoursDesc.style.color = "#536471";
                              hoursDesc.innerText = conn.OpenHours.String && conn.CloseHours.String 
                                ? conn.CloseHours.String === "Closed" ? "Closed" : `Today at ${conn.OpenHours.String} - ${conn.CloseHours.String}` 
                                : "Closed";
                              
                              col22.appendChild(hoursDesc);
                              row2.appendChild(col2);
                              row2.appendChild(col22);
                              descriptionBlock.appendChild(row2);
                              
                              const row3 = document.createElement("div");
                              row3.classList.add("row");
                              
                              const col8 = document.createElement("div");
                              col8.classList.add("col-8", "text-end");
                              
                              const distanceImg = document.createElement("img");
                              distanceImg.src = "/files/distance.png";
                              distanceImg.alt = "Clock icon representing hours";
                              
                              col8.appendChild(distanceImg);
                              
                              const col4 = document.createElement("div");
                              col4.classList.add("col-4", "ps-0");
                              
                              const distanceDesc = document.createElement("p");
                              distanceDesc.classList.add("description");
                              distanceDesc.style.color = "#536471";
                              distanceDesc.innerText = `${(parseFloat(conn.Distance) / 1000).toFixed(2)} km`;
                              
                              col4.appendChild(distanceDesc);
                              row3.appendChild(col8);
                              row3.appendChild(col4);
                              descriptionBlock.appendChild(row3);
                              
                              cardContent.appendChild(descriptionBlock);

                              const col = document.createElement("div");
                              col.classList.add("col-md-4", "col-sm-12");
                              col.appendChild(cardContent);

                              container.appendChild(col);
                          });
                      }
                      dropdown.innerHTML = ""; // Clear previous results
                      if (data.length > 0) {
                      dropdown.style.display = "block";
                      data.forEach(conn => {
                          const option = document.createElement("option");
                          option.value = conn.id;
                          option.textContent = conn.Name;
                          dropdown.appendChild(option);
                      });
                      } else {
                      dropdown.style.display = "none";
                      }
                  })
                  .catch(error => console.error("Error fetching data:", error));
            searchInput.addEventListener("input", () => {
              console.log("input");
              const query = searchInput.value;
              if (query.length > 0) {
                  const brochureIdInput = document.querySelector('input[name="brochure_id"]');
                  const brochureId = brochureIdInput.value;
                  fetch(`/get-order-store?q=${encodeURIComponent(query)}&lat=${encodeURIComponent(lat)}&long=${encodeURIComponent(long)}&brochure_id=${encodeURIComponent(brochureId)}`)
                  .then(response => response.json())
                  .then(data => {
                      const container = document.getElementById("storeLocRealtime");
                      container.innerHTML = "";
                      if (data.length > 0) {
                          data.forEach(conn => {
                            
                              const cardContent = document.createElement("div");
                              cardContent.classList.add("card-content");
                              cardContent.style.boxShadow = "-3px 6px 8px 0 rgba(0, 0, 0, 0.2)";
                              
                              const row = document.createElement("div");
                              row.classList.add("row");
                              
                              const col12 = document.createElement("div");
                              col12.classList.add("col-12");
                              
                              const locationTitle = document.createElement("h2");
                              locationTitle.classList.add("location-title");
                              locationTitle.innerText = conn.Name;
                              
                              col12.appendChild(locationTitle);
                              row.appendChild(col12);
                              cardContent.appendChild(row);
                              
                              const descriptionBlock = document.createElement("div");
                              descriptionBlock.classList.add("description-block");
                              
                              const row1 = document.createElement("div");
                              row1.classList.add("row");
                              
                              const col1 = document.createElement("div");
                              col1.classList.add("col-1");
                              
                              const mapImg = document.createElement("img");
                              mapImg.src = "/files/map.png";
                              mapImg.alt = "Map image for location";
                              
                              col1.appendChild(mapImg);
                              
                              const col11 = document.createElement("div");
                              col11.classList.add("col-11", "ps-0");
                              
                              const locationDesc = document.createElement("p");
                              locationDesc.classList.add("description");
                              locationDesc.style.color = "#536471";
                              locationDesc.innerText = conn.Location;
                              
                              col11.appendChild(locationDesc);
                              row1.appendChild(col1);
                              row1.appendChild(col11);
                              descriptionBlock.appendChild(row1);
                              
                              const row2 = document.createElement("div");
                              row2.classList.add("row");
                              
                              const col2 = document.createElement("div");
                              col2.classList.add("col-1");
                              
                              const clockImg = document.createElement("img");
                              clockImg.src = "/files/clock.png";
                              clockImg.alt = "Clock icon representing hours";
                              
                              col2.appendChild(clockImg);
                              
                              const col22 = document.createElement("div");
                              col22.classList.add("col-11", "ps-0");
                              
                              const hoursDesc = document.createElement("p");
                              hoursDesc.classList.add("description");
                              hoursDesc.style.color = "#536471";
                              hoursDesc.innerText = conn.OpenHours.String && conn.CloseHours.String 
                                ? conn.CloseHours.String === "Closed" ? "Closed" : `Today at ${conn.OpenHours.String} - ${conn.CloseHours.String}` 
                                : "Closed";
                              
                              col22.appendChild(hoursDesc);
                              row2.appendChild(col2);
                              row2.appendChild(col22);
                              descriptionBlock.appendChild(row2);
                              
                              const row3 = document.createElement("div");
                              row3.classList.add("row");
                              
                              const col8 = document.createElement("div");
                              col8.classList.add("col-8", "text-end");
                              
                              const distanceImg = document.createElement("img");
                              distanceImg.src = "/files/distance.png";
                              distanceImg.alt = "Clock icon representing hours";
                              
                              col8.appendChild(distanceImg);
                              
                              const col4 = document.createElement("div");
                              col4.classList.add("col-4", "ps-0");
                              
                              const distanceDesc = document.createElement("p");
                              distanceDesc.classList.add("description");
                              distanceDesc.style.color = "#536471";
                              distanceDesc.innerText = `${(parseFloat(conn.Distance) / 1000).toFixed(2)} km`;
                              
                              col4.appendChild(distanceDesc);
                              row3.appendChild(col8);
                              row3.appendChild(col4);
                              descriptionBlock.appendChild(row3);
                              
                              cardContent.appendChild(descriptionBlock);

                              const col = document.createElement("div");
                              col.classList.add("col-md-4", "col-sm-12");
                              col.appendChild(cardContent);

                              container.appendChild(col);
                          });
                      }
                      dropdown.innerHTML = ""; // Clear previous results
                      if (data.length > 0) {
                      dropdown.style.display = "block";
                      data.forEach(conn => {
                          const option = document.createElement("option");
                          option.value = conn.id;
                          option.textContent = conn.Name;
                          dropdown.appendChild(option);
                      });
                      } else {
                      dropdown.style.display = "none";
                      }
                  })
                  .catch(error => console.error("Error fetching data:", error));
              } else {
                  dropdown.style.display = "none";
              }
            });

            dropdown.addEventListener("change", () => {
              const selectedOption = dropdown.options[dropdown.selectedIndex];
              searchInput.value = selectedOption.textContent;
              dropdown.style.display = "none";
            });

            // Hide dropdown when clicking outside
            document.addEventListener("click", (e) => {
              if (!dropdown.contains(e.target) && e.target !== searchInput) {
                  dropdown.style.display = "none";
              }
            });
          },
          (error) => {
            console.error("Error getting location:", error);
            switch (error.code) {
              case error.PERMISSION_DENIED:
                alert("User denied the request for Geolocation.");
                break;
              case error.POSITION_UNAVAILABLE:
                alert("Location information is unavailable.");
                break;
              case error.TIMEOUT:
                alert("The request to get user location timed out.");
                break;
              case error.UNKNOWN_ERROR:
                alert("An unknown error occurred.");
                break;
            }
          }
        );
      } else {
        alert("Geolocation is not supported by this browser.");
      }
    });
  </script>
</body>
</html>
