server {
    listen 80;
    # server_name yourdomain.com;  # Replace with your domain

    # Redirect HTTP to HTTPS
    location / {
        proxy_pass http://app:8010;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Optionally redirect to HTTPS
    # if ($scheme = http) {
    #     return 301 https://$host$request_uri;
    # }
}