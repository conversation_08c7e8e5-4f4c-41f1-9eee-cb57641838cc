package controllers

import (
	"net/http"
	"strconv"

	"e-brochure/models"
	"e-brochure/repositories"

	"github.com/gin-gonic/gin"
)

// InfluencerController handles influencer-related HTTP requests
type InfluencerController struct {
	repo repositories.InfluencerRepository
}

// NewInfluencerController creates a new InfluencerController
func NewInfluencerController(r repositories.InfluencerRepository) *InfluencerController {
	return &InfluencerController{repo: r}
}

// GetAllInfluencers retrieves all influencers
func (ctrl *InfluencerController) GetAllInfluencers(c *gin.Context) {
	influencers, err := ctrl.repo.GetAll()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve influencers", "details": err.Error()})
	}

	c.<PERSON>(http.StatusOK, influencers)
}

// GetInfluencerByID retrieves an influencer by ID
func (ctrl *InfluencerController) GetInfluencerByID(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid influencer ID"})
		return
	}

	influencer, err := ctrl.repo.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Influencer not found"})
		return
	}

	c.JSON(http.StatusOK, influencer)
}

// CreateInfluencer creates a new influencer
func (ctrl *InfluencerController) CreateInfluencer(c *gin.Context) {
	var influencer models.Influencer
	if err := c.BindJSON(&influencer); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}

	if err := ctrl.repo.Create(&influencer); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create influencer"})
		return
	}

	c.JSON(http.StatusCreated, influencer)
}

// UpdateInfluencer updates an influencer
func (ctrl *InfluencerController) UpdateInfluencer(c *gin.Context) {
	_, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid influencer ID"})
		return
	}

	var influencer models.Influencer
	if err := c.BindJSON(&influencer); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}

	if err := ctrl.repo.Update(&influencer); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update influencer"})
		return
	}

	c.JSON(http.StatusOK, influencer)
}

// DeleteInfluencer deletes an influencer
func (ctrl *InfluencerController) DeleteInfluencer(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid influencer ID"})
		return
	}

	if err := ctrl.repo.Delete(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete influencer"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Influencer deleted successfully"})
}
