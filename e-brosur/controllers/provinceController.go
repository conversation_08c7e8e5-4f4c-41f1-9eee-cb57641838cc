package controllers

import (
	"net/http"
	"strconv"

	"e-brochure/models"
	"e-brochure/repositories"

	"github.com/gin-gonic/gin"
)

// ProvinceController handles province-related HTTP requests
type ProvinceController struct {
	repo repositories.ProvinceRepository
}

// NewProvinceController creates a new ProvinceController
func NewProvinceController(repo repositories.ProvinceRepository) *ProvinceController {
	return &ProvinceController{repo: repo}
}

// GetAllProvinces retrieves all provinces
func (ctrl *ProvinceController) GetAllProvinces(c *gin.Context) {
	provinces, err := ctrl.repo.GetAll()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, provinces)
}

// GetProvinceByID retrieves a single province by ID
func (ctrl *ProvinceController) GetProvinceByID(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}
	province, err := ctrl.repo.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	if province == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Province not found"})
		return
	}
	c.JSON(http.StatusOK, province)
}

// CreateProvince creates a new province
func (ctrl *ProvinceController) CreateProvince(c *gin.Context) {
	var province models.Province
	if err := c.ShouldBindJSON(&province); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := ctrl.repo.Create(&province); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, province)
}

// UpdateProvince updates an existing province
func (ctrl *ProvinceController) UpdateProvince(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	var province models.Province
	if err := c.ShouldBindJSON(&province); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	province.ID = uint(id)

	if err := ctrl.repo.Update(&province); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, province)
}

// DeleteProvince deletes a province by ID
func (ctrl *ProvinceController) DeleteProvince(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	if err := ctrl.repo.Delete(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Province deleted"})
}
