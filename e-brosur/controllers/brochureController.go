package controllers

import (
	"encoding/json"
	"fmt"
	"html/template"
	"io"
	"log"
	"math/big"
	"math/rand"
	"net/http"
	"net/url"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	"e-brochure/models"
	"e-brochure/repositories"
	"e-brochure/services"

	"github.com/gin-gonic/gin"
	"github.com/skip2/go-qrcode"
)

// BrochureController handles brochure-related HTTP requests
type BrochureController struct {
	repoVisitorProfile repositories.VisitorProfileRepository
	repoVisitor repositories.VisitorRepository
	repo repositories.BrochureRepository
	repoRotator repositories.RotatorURLRepository
	repoPayload repositories.PayloadRepository
	repoStore repositories.StoreRepository
	repoUser repositories.UserRepository
	repoQrCode repositories.QrCodeRepository
}

type SimulateBrochure struct {
	ID         int
	Impressions int
	Clicks      int
	Alpha       float64 // Successes for Beta distribution
	Beta        float64 // Failures for Beta distribution
}

// NewBrochureController creates a new BrochureController
func NewBrochureController(repo repositories.BrochureRepository, repoRotator repositories.RotatorURLRepository, repoVisitorProfile repositories.VisitorProfileRepository, repoPayload repositories.PayloadRepository, repoStore repositories.StoreRepository, repoUser repositories.UserRepository, repoVisitor repositories.VisitorRepository, repoQrCode repositories.QrCodeRepository) *BrochureController {
	return &BrochureController{
		repoVisitorProfile:        repoVisitorProfile,
		repo:        repo,
		repoRotator: repoRotator,
		repoPayload: repoPayload,
		repoStore:   repoStore,
		repoUser:   repoUser,
		repoVisitor:   repoVisitor,
		repoQrCode: repoQrCode,
	}
}

// CreateBrochure creates a new brochure
func (bc *BrochureController) CreateBrochure(c *gin.Context) {
	var input struct {
		Title    string `json:"title"`
		Body     string `json:"body"`
		StoreIDs []uint `json:"store_ids"`
		Status   string `json:"status"`
	}
	// Get user ID from the context
	currentUser, exists := c.Get("userKey")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User ID not found in context"})
		return
	}

	// Retrieve the user from the repository
	user, err := bc.repoUser.GetByUserKey(currentUser.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve user project key"})
		return
	}
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user project key"})
		return
	}
	if user == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}
	// Bind JSON input to the input struct
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input"})
		return
	}
	if user.ProjectKey == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User project key is required"})
		return
	}

	// Create a new brochure model
	brochure := models.Brochure{
		Title:  input.Title,
		Body:   input.Body,
		Status: input.Status,
		ProjectKey: user.ProjectKey,
	}
	// Create the brochure using the repository
	if err := bc.repo.Create(&brochure); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create brochure", "details": err.Error()})
		return
	}

	// Get thumbnail from screenshot service
	url := fmt.Sprintf("http://%s/preview-brochure/%d", os.Getenv("APPLICATION_URL"), brochure.ID)
	thumbnail, err := services.GetScreenshot(url,"1920","1080")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get screenshot"})
		return
	}
	// Save the generated thumbnail
	var thumbnailResponse struct {
		Error   bool `json:"error"`
		Message string `json:"message"`
		Data    struct {
			Namefile string `json:"namefile"`
			FileURL  string `json:"fileURL"`
		} `json:"data"`
	}
	if err := json.Unmarshal(thumbnail, &thumbnailResponse); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse thumbnail response"})
		return
	}
	log.Println(thumbnailResponse)
	brochure.Thumbnail = thumbnailResponse.Data.FileURL
	if err := bc.repo.Update(&brochure); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update brochure with thumbnail"})
		return
	}

	// Associate the brochure with the specified stores
	for _, storeID := range input.StoreIDs {
		storeBrochure := models.BrochureStore{
			StoreID:    storeID,
			BrochureID: brochure.ID,
		}
		if err := bc.repo.CreateStoreBrochure(&storeBrochure); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to associate brochure with store"})
			return
		}
	}

	c.JSON(http.StatusCreated, brochure)
}

// GetBrochures retrieves all brochures
func (bc *BrochureController) GetBrochures(c *gin.Context) {
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid page number"})
		return
	}
	limit, err := strconv.Atoi(c.DefaultQuery("limit", "10"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit"})
		return
	}

	title := c.Query("search")
	var brochures []models.Brochure
	var total int64
	user, err := bc.getUser(c)
	if err != nil {
		return
	}
	if title != "" {
		var err error
		brochures, err = bc.repo.GetByTitlePaged(title, page, limit, user.ProjectKey)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve brochures by title"})
			return
		}

		total, err = bc.repo.CountByTitle(title)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count brochures by title"})
			return
		}
	} else {
		var err error
		brochures, err = bc.repo.GetAllPaged(page, limit, user.ProjectKey)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve brochures"})
			return
		}

		total, err = bc.repo.Count()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count brochures"})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"data":      brochures,
		"page":      page,
		"limit":     limit,
		"total":     total,
		"totalPage": int((total + int64(limit) - 1) / int64(limit)),
	})
}

// GetBrochure retrieves a single brochure by ID
func (bc *BrochureController) GetBrochure(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid brochure ID"})
		return
	}

	brochure, err := bc.repo.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve brochure"})
		return
	}
	if brochure == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Brochure not found"})
		return
	}

	c.JSON(http.StatusOK, brochure)
}

// UpdateBrochure updates an existing brochure
func (bc *BrochureController) UpdateBrochure(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid brochure ID"})
		return
	}

	var brochure models.Brochure
	if err := c.ShouldBindJSON(&brochure); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input"})
		return
	}

	user, err := bc.getUser(c)
	if err != nil {
		return
	}

	brochure.ProjectKey = user.ProjectKey
	brochure.ID = uint(id) // Set the brochure ID from the URL parameter

	// Update brochure using the repository
	if err := bc.repo.Update(&brochure); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update brochure"})
		return
	}

	c.JSON(http.StatusOK, brochure)
}

// DeleteBrochure deletes a brochure by ID
func (bc *BrochureController) DeleteBrochure(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := bc.repo.Delete(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Brochure deleted successfully"})
}
func (bc *BrochureController) RenderView() string {
	return `<html>
        <head>
          <script src="https://builder.gass.co.id/assets/libs/splidejs/js/splide.js"></script>
          <link rel="stylesheet" href="https://builder.gass.co.id/assets/libs/splidejs/css/splide.min.css">
          <style>
            * { box-sizing: border-box; } body {margin: 0;}*{box-sizing:border-box;}body{margin-top:0px;margin-right:0px;margin-bottom:0px;margin-left:0px;}[data-gjs-type="custom-radio"] *, [data-gjs-type="custom-checkbox"] *, [data-pointer="false"]{pointer-events:none;}textarea{max-width:100%;}input[type="radio"], input[type="checkbox"]{appearance:none;padding-top:7px;padding-right:7px;padding-bottom:7px;padding-left:7px;background-color:rgb(242, 248, 255);border-top-width:1px;border-top-style:solid;border-top-color:rgb(95, 108, 122);border-right-width:1px;border-right-style:solid;border-right-color:rgb(95, 108, 122);border-bottom-width:1px;border-bottom-style:solid;border-bottom-color:rgb(95, 108, 122);border-left-width:1px;border-left-style:solid;border-left-color:rgb(95, 108, 122);border-image-outset:0;border-image-repeat:stretch;border-image-slice:100%;border-image-source:none;border-image-width:1;border-top-left-radius:50%;border-top-right-radius:50%;border-bottom-right-radius:50%;border-bottom-left-radius:50%;}input[type="checkbox"]{border-top-left-radius:2% !important;border-top-right-radius:2% !important;border-bottom-right-radius:2% !important;border-bottom-left-radius:2% !important;}input[type="radio"]:checked, input[type="checkbox"]:checked{background-color:var(--checked-color);}.visitor-qrcode{position:relative;min-height:250px;display:flex;align-items:center;justify-content:center;}.visitor-qrcode img.background{width:100%;}.visitor-qrcode .qrcode-wrapper{width:100px;height:100px;position:absolute;z-index:1;top:50%;left:50%;transform:translate(-50%, -50%);}.visitor-qrcode img.qrcode{width:100%;height:100%;}.visitor-qrcode img.logo{width:16%;height:16%;position:absolute;z-index:1;top:50%;left:50%;transform:translate(-50%, -50%);object-fit:contain;background-color:rgb(255, 255, 255);background-position-x:0%;background-position-y:0%;background-repeat:repeat;background-attachment:scroll;background-image:none;background-size:auto;background-origin:padding-box;background-clip:border-box;}.visitor-qrcode [src="-"]{display:none;}
          </style>
        </head>
        <body>
          <section class="visitor-qrcode"><img src="null" data-layerable="false" data-pointer="false" class="background"><div data-layerable="false" data-pointer="false" class="qrcode-wrapper"><img src="null" alt="QR Code" data-layerable="false" data-pointer="false" class="qrcode"><img src="null" data-layerable="false" data-pointer="false" class="logo"></div></section>
        </body>
      </html>`;
//     return `<!DOCTYPE html>
//         <html lang="en">
//            <head>
// <!-- Google tag (gtag.js) -->
// <script async src="https://www.googletagmanager.com/gtag/js?id=AW-11384115888"></script>
// <script id="system-script">
// </script>
// <script id="custom-script">
// </script>
// <script>
//   window.dataLayer = window.dataLayer || [];
//   function gtag(){dataLayer.push(arguments);}
//   gtag('js', new Date());

//   gtag('config', 'AW-11384115888');
// </script>
        
//               <meta charset="utf-8">
//               <meta name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1,maximum-scale=5">
//               <meta name="apple-mobile-web-app-capable" content="yes">
//               <meta name="apple-mobile-web-app-status-bar-style" content="black">
//               <style>
//               body,
//       img,
//       picture {
//         padding: 0;
//         margin: 0;
//         border: 0;
//         display: block
//       }

//       body {
//         background-color: #dbdbdb;
//         max-width: 1200px;
//         margin: auto
//       }

//       img {
//         width: 100%;
//         height: auto
//       }

//       @media (orientation: landscape) {
//         #portrait {
//           display: none
//         }

//         #landscape {
//           display: block
//         }
//       }

//       @media (orientation: portrait) {
//         #landscape {
//           display: none
//         }

//         #portrait {
//           display: block
//         }
//       }

//               </style>
        
//            </head>
//            <body><div id="landscape">
//           <a id="ie7w"><picture data-layerable="false" data-pointer="false" id="ixso"><source srcset="https://media1.gass.co.id/images/i.9.CiXbPsrDeZ_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXbPsrDeZ_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXbPsrDeZ_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="iusk"></picture></a><a href="https://lp.gass.co.id/r1.html?v=4LN7&is_gass=1&gass=c.gass.co.id&is_mobile=0&act=cta_redirect&host=lp.gass.co.id&path=%2Fr1.html&variant_id=v.4.D.CicX9vrHiQ.%2AGassDefault%2A.CiXd2HWrDt&cta=https%3A%2F%2Fc.gass.co.id%2Fcta%3Fd%3Dkontak-masuk%26amp%3Bmsg%3DHalo%2520min%250abisa%2520minta%2520info%2520tentang%2520gass%2520nya%2520%3F%2520%3F%250aterima%2520kasih%26amp%3Bp%3D49C783F982CCB5457AFEAF51423D8E31" target="_blank" id="imcd"><picture data-layerable="false" data-pointer="false" id="ik3t"><source srcset="https://media1.gass.co.id/images/i.9.CiXbcPeeyR_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXbcPeeyR_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXbcPeeyR_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="ixdl9"></picture></a><a id="ifbdx"><picture data-layerable="false" data-pointer="false" id="ildxb"><source srcset="https://media1.gass.co.id/images/i.9.CiXbcvNCmP_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXbcvNCmP_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXbcvNCmP_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="i271n"></picture></a><a id="ip5zt"><picture data-layerable="false" data-pointer="false" id="ifvjk"><source srcset="https://media1.gass.co.id/images/i.9.CiXbdS1N17_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXbdS1N17_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXbdS1N17_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="ip964"></picture></a><a id="iekvd"><picture data-layerable="false" data-pointer="false" id="i399g"><source srcset="https://media1.gass.co.id/images/i.9.CiXbe4jq24_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXbe4jq24_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXbe4jq24_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="ic6ic"></picture></a><a id="i7v6b"><picture data-layerable="false" data-pointer="false" id="ienhf"><source srcset="https://media1.gass.co.id/images/i.9.CiXbeega0D_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXbeega0D_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXbeega0D_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="iaqxf"></picture></a><a id="im166"><picture data-layerable="false" data-pointer="false" id="i7oy9"><source srcset="https://media1.gass.co.id/images/i.9.CiXblcu7da_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXblcu7da_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXblcu7da_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="iw3rl"></picture></a><a id="isbf1"><picture data-layerable="false" data-pointer="false" id="izg3c"><source srcset="https://media1.gass.co.id/images/i.9.CiXbnEjvYu_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXbnEjvYu_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXbnEjvYu_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="iwomj"></picture></a><a id="ig47e"><picture data-layerable="false" data-pointer="false" id="iuum1"><source srcset="https://media1.gass.co.id/images/i.9.CiXboWIXY7_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXboWIXY7_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXboWIXY7_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="isk2o"></picture></a><a id="iqemx"><picture data-layerable="false" data-pointer="false" id="ie6iz"><source srcset="https://media1.gass.co.id/images/i.9.CiXbosIQbx_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXbosIQbx_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXbosIQbx_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="ipgsl"></picture></a><a id="ips04"><picture data-layerable="false" data-pointer="false" id="in90k"><source srcset="https://media1.gass.co.id/images/i.9.CiXbp8rNgk_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXbp8rNgk_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXbp8rNgk_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="iuqwn"></picture></a><a id="i3hrt"><picture data-layerable="false" data-pointer="false" id="iis6c"><source srcset="https://media1.gass.co.id/images/i.4.CiXiAi2wIL_large.webp"><source srcset="https://media1.gass.co.id/images/i.4.CiXiAi2wIL_medium.webp"><source srcset="https://media1.gass.co.id/images/i.4.CiXiAi2wIL_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="igyen"></picture></a><a href="https://lp.gass.co.id/r1.html?v=4LN7&is_gass=1&gass=c.gass.co.id&is_mobile=0&act=cta_redirect&host=lp.gass.co.id&path=%2Fr1.html&variant_id=v.4.D.CicX9vrHiQ.%2AGassDefault%2A.CiXd2HWrDt&cta=https%3A%2F%2Fc.gass.co.id%2Fcta%3Fd%3Dkontak-masuk%26amp%3Bmsg%3DHalo%2520min%250abisa%2520minta%2520info%2520tentang%2520gass%2520nya%2520%3F%2520%3F%250aterima%2520kasih%26amp%3Bp%3D49C783F982CCB5457AFEAF51423D8E31" target="_blank" id="i9cfe"><picture data-layerable="false" data-pointer="false" id="i2niy"><source srcset="https://media1.gass.co.id/images/i.4.CiXiB2qByK_large.webp"><source srcset="https://media1.gass.co.id/images/i.4.CiXiB2qByK_medium.webp"><source srcset="https://media1.gass.co.id/images/i.4.CiXiB2qByK_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="ihh5l"></picture></a><a id="i413i"><picture data-layerable="false" data-pointer="false" id="irgpq"><source srcset="https://media1.gass.co.id/images/i.4.CiXiBHEN3T_large.webp"><source srcset="https://media1.gass.co.id/images/i.4.CiXiBHEN3T_medium.webp"><source srcset="https://media1.gass.co.id/images/i.4.CiXiBHEN3T_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="iggvo"></picture></a><a id="iw9tu"><picture data-layerable="false" data-pointer="false" id="idlak"><source srcset="https://media1.gass.co.id/images/i.4.CiXiEMqWZU_large.webp"><source srcset="https://media1.gass.co.id/images/i.4.CiXiEMqWZU_medium.webp"><source srcset="https://media1.gass.co.id/images/i.4.CiXiEMqWZU_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="ikxwn"></picture></a><a id="ipikt"><picture data-layerable="false" data-pointer="false" id="is4q6"><source srcset="https://media1.gass.co.id/images/i.4.CiXiEj0vbA_large.webp"><source srcset="https://media1.gass.co.id/images/i.4.CiXiEj0vbA_medium.webp"><source srcset="https://media1.gass.co.id/images/i.4.CiXiEj0vbA_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="i4hgp"></picture></a><a href="https://lp.gass.co.id/r1.html?v=4LN7&is_gass=1&gass=c.gass.co.id&is_mobile=0&act=cta_redirect&host=lp.gass.co.id&path=%2Fr1.html&variant_id=v.4.D.CicX9vrHiQ.%2AGassDefault%2A.CiXd2HWrDt&cta=https%3A%2F%2Fc.gass.co.id%2Fcta%3Fd%3Dkontak-masuk%26amp%3Bmsg%3DHalo%2520min%250abisa%2520minta%2520info%2520tentang%2520gass%2520nya%2520%3F%2520%3F%250aterima%2520kasih%26amp%3Bp%3D49C783F982CCB5457AFEAF51423D8E31" target="_blank" id="i96ii"><picture data-layerable="false" data-pointer="false" id="ik7ul"><source srcset="https://media1.gass.co.id/images/i.4.CiXiEw2Ncc_large.webp"><source srcset="https://media1.gass.co.id/images/i.4.CiXiEw2Ncc_medium.webp"><source srcset="https://media1.gass.co.id/images/i.4.CiXiEw2Ncc_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="ih5zv7"></picture></a><a id="iytnta"><picture data-layerable="false" data-pointer="false" id="it47ek"><source srcset="https://media1.gass.co.id/images/i.4.CiXiFD9VuM_large.webp"><source srcset="https://media1.gass.co.id/images/i.4.CiXiFD9VuM_medium.webp"><source srcset="https://media1.gass.co.id/images/i.4.CiXiFD9VuM_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="ionr4e"></picture></a>
//         </div><div id="portrait">
//           <a id="iyy7"><picture data-layerable="false" data-pointer="false" id="iarw"><source srcset="https://media1.gass.co.id/images/i.9.CiXdNQNZ6W_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXdNQNZ6W_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXdNQNZ6W_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="ikhf"></picture></a><a href="https://lp.gass.co.id/r1.html?v=4LN7&is_gass=1&gass=c.gass.co.id&is_mobile=0&act=cta_redirect&host=lp.gass.co.id&path=%2Fr1.html&variant_id=v.4.D.CicX9vrHiQ.%2AGassDefault%2A.CiXd2HWrDt&cta=https%3A%2F%2Fc.gass.co.id%2Fcta%3Fd%3Dkontak-masuk%26amp%3Bmsg%3DHalo%2520min%250abisa%2520minta%2520info%2520tentang%2520gass%2520nya%2520%3F%2520%3F%250aterima%2520kasih%26amp%3Bp%3D49C783F982CCB5457AFEAF51423D8E31" target="_blank" id="icq8"><picture data-layerable="false" data-pointer="false" id="iqmb"><source srcset="https://media1.gass.co.id/images/i.9.CiXdoMuEsr_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXdoMuEsr_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXdoMuEsr_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="i72mm"></picture></a><a id="i34f4"><picture data-layerable="false" data-pointer="false" id="imldd"><source srcset="https://media1.gass.co.id/images/i.9.CiXdp2W73F_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXdp2W73F_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXdp2W73F_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="imcgd"></picture></a><a id="it47g"><picture data-layerable="false" data-pointer="false" id="ixmzf"><source srcset="https://media1.gass.co.id/images/i.9.CiXdpUhlaF_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXdpUhlaF_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXdpUhlaF_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="ivxnv"></picture></a><a id="i5htk"><picture data-layerable="false" data-pointer="false" id="iumpn"><source srcset="https://media1.gass.co.id/images/i.9.1GqxmaktXA_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.1GqxmaktXA_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.1GqxmaktXA_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="iwttu"></picture></a><a id="i15wp"><picture data-layerable="false" data-pointer="false" id="iwmzg"><source srcset="https://media1.gass.co.id/images/i.9.CiXdqNAnoz_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXdqNAnoz_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXdqNAnoz_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="i3zvi"></picture></a><a id="i85tr"><picture data-layerable="false" data-pointer="false" id="ic05j"><source srcset="https://media1.gass.co.id/images/i.9.CiXfTJvkkY_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXfTJvkkY_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXfTJvkkY_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="i8vcz"></picture></a><a id="inuml"><picture data-layerable="false" data-pointer="false" id="isvnu"><source srcset="https://media1.gass.co.id/images/i.9.CiXfTgmJFW_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXfTgmJFW_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXfTgmJFW_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="iwxqk"></picture></a><a id="itc7e"><picture data-layerable="false" data-pointer="false" id="ix4w8"><source srcset="https://media1.gass.co.id/images/i.9.CiXfUGbDfM_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXfUGbDfM_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXfUGbDfM_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="i2g1x"></picture></a><a id="ig0x6"><picture data-layerable="false" data-pointer="false" id="ilzgy"><source srcset="https://media1.gass.co.id/images/i.9.CiXfUZWIhN_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXfUZWIhN_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXfUZWIhN_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="icl6u"></picture></a><a id="ihucd"><picture data-layerable="false" data-pointer="false" id="iozzg"><source srcset="https://media1.gass.co.id/images/i.9.CiXfV4hFhr_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXfV4hFhr_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXfV4hFhr_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="i1efs"></picture></a><a id="ic94h"><picture data-layerable="false" data-pointer="false" id="it91f"><source srcset="https://media1.gass.co.id/images/i.9.1Gqxww9nde_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.1Gqxww9nde_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.1Gqxww9nde_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="im4qr"></picture></a><a id="ip67d"><picture data-layerable="false" data-pointer="false" id="ix8u2"><source srcset="https://media1.gass.co.id/images/i.9.1Gqy0ypLkZ_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.1Gqy0ypLkZ_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.1Gqy0ypLkZ_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="i25m2"></picture></a><a id="imm8e"><picture data-layerable="false" data-pointer="false" id="i1w12"><source srcset="https://media1.gass.co.id/images/i.9.CiXgAhUKjH_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXgAhUKjH_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXgAhUKjH_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="i7ugq"></picture></a><a id="ibhb1"><picture data-layerable="false" data-pointer="false" id="incob"><source srcset="https://media1.gass.co.id/images/i.9.CiXgBcJl4z_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXgBcJl4z_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXgBcJl4z_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="i2z8k"></picture></a><a href="https://lp.gass.co.id/r1.html?v=4LN7&is_gass=1&gass=c.gass.co.id&is_mobile=0&act=cta_redirect&host=lp.gass.co.id&path=%2Fr1.html&variant_id=v.4.D.CicX9vrHiQ.%2AGassDefault%2A.CiXd2HWrDt&cta=https%3A%2F%2Fc.gass.co.id%2Fcta%3Fd%3Dkontak-masuk%26amp%3Bmsg%3DHalo%2520min%250abisa%2520minta%2520info%2520tentang%2520gass%2520nya%2520%3F%2520%3F%250aterima%2520kasih%26amp%3Bp%3D49C783F982CCB5457AFEAF51423D8E31" target="_blank" id="ii0yf"><picture data-layerable="false" data-pointer="false" id="iyaih"><source srcset="https://media1.gass.co.id/images/i.9.CiXgCNHvHs_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXgCNHvHs_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXgCNHvHs_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="ia9mx"></picture></a><a id="iuk0a"><picture data-layerable="false" data-pointer="false" id="iwld8"><source srcset="https://media1.gass.co.id/images/i.9.CiXgDBWB88_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXgDBWB88_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXgDBWB88_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="ielvkg"></picture></a><a id="i6wru4"><picture data-layerable="false" data-pointer="false" id="i4euzm"><source srcset="https://media1.gass.co.id/images/i.9.CiXgE0K1D8_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXgE0K1D8_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXgE0K1D8_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="i683qf"></picture></a><a id="i1ks9j"><picture data-layerable="false" data-pointer="false" id="i84nux"><source srcset="https://media1.gass.co.id/images/i.9.CiXgpXVbcc_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXgpXVbcc_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXgpXVbcc_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="i8gt09"></picture></a><a id="i78pky"><picture data-layerable="false" data-pointer="false" id="i4rncq"><source srcset="https://media1.gass.co.id/images/i.9.CiXgpsEqib_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXgpsEqib_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXgpsEqib_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="idja42"></picture></a><a href="https://lp.gass.co.id/r1.html?v=4LN7&is_gass=1&gass=c.gass.co.id&is_mobile=0&act=cta_redirect&host=lp.gass.co.id&path=%2Fr1.html&variant_id=v.4.D.CicX9vrHiQ.%2AGassDefault%2A.CiXd2HWrDt&cta=https%3A%2F%2Fc.gass.co.id%2Fcta%3Fd%3Dkontak-masuk%26amp%3Bmsg%3DHalo%2520min%250abisa%2520minta%2520info%2520tentang%2520gass%2520nya%2520%3F%2520%3F%250aterima%2520kasih%26amp%3Bp%3D49C783F982CCB5457AFEAF51423D8E31" target="_blank" id="i83y8l"><picture data-layerable="false" data-pointer="false" id="id5b29"><source srcset="https://media1.gass.co.id/images/i.9.CiXgq7eqLv_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXgq7eqLv_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXgq7eqLv_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="is8n34"></picture></a><a id="i3nqkg"><picture data-layerable="false" data-pointer="false" id="iyl25m"><source srcset="https://media1.gass.co.id/images/i.9.CiXgqH5H61_large.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXgqH5H61_medium.webp"><source srcset="https://media1.gass.co.id/images/i.9.CiXgqH5H61_small.webp"><img data-layerable="false" data-pointer="false" src="null" id="ijpxas"></picture></a>
//         </div>
        
//          <script>   function setupImage() {
//       const imagesCount = document.querySelectorAll("img").length;
//       function setSrc(index) {
//         const img = document.querySelectorAll("img")[index];
//         if (img) {
//           if (img.getAttribute("data-src")) {
//             img.setAttribute("src", img.getAttribute("data-src"));
//           }

//           if (img.previousElementSibling.getAttribute("data-srcset")) {
//             img.previousElementSibling.setAttribute(
//               "srcset",
//               img.previousElementSibling.getAttribute("data-srcset")
//             );
//           }

//           if (img.previousElementSibling.previousElementSibling.getAttribute(
//             "data-srcset"
//           )) {
//             img.previousElementSibling.previousElementSibling.setAttribute(
//               "srcset",
//               img.previousElementSibling.previousElementSibling.getAttribute(
//                 "data-srcset"
//               )
//             );
//           }

//           if (index <= imagesCount) {
//             img.addEventListener("load", () => {
//               index++;
//               setSrc(index);
//             });
//           }
//         }
//       }
//       setSrc(0);
//     }

//     window.addEventListener("resize", function () {
//       setupImage();
//     });
//     document.addEventListener("DOMContentLoaded", function () {
//       setupImage();
//     });
//   </script></body></html>`;
}
func (bc *BrochureController) RenderJs(c *gin.Context) {
	urlParam := c.Param("url")
	rotator, err2 := bc.repoRotator.GetByShortURL(urlParam)
	if err2 != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve RotatorURL"})
		return
	}
	if rotator == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "RotatorURL not found"})
		return
	}
	visitorID := strconv.FormatUint(uint64(rotator.ID), 10) + ".1.1.3123" 
	result := bc.getConv(visitorID)

	visitorID, err := c.Cookie("visitor_id")
	if err != nil {
		visitorID = result
    // Insert new data to visitor_profile table
    var visitorProfile models.VisitorProfile
    visitorProfile.IP = c.ClientIP()
    visitorProfile.BrowserAgent = c.Request.UserAgent()
    visitorProfile.Referral = c.Request.Referer()
    visitorProfile.VisitorID = result

    if err3 := c.Bind(&visitorProfile); err3 != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err3.Error()})
        return
    }
    if bc.repoVisitorProfile == nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "repository is nil"})
        return
    }
    if err = bc.repoVisitorProfile.Create(&visitorProfile); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
		c.SetCookie("visitor_id", result, 3600*24*30, "/", "e-brosur.gass.co.id", false, true)
	}

	config := map[string]interface{}{
		"visitor_id":       visitorID, 
		"project_key":      "DcZKCu",
		"api_endpoint_url": "https://e-brosur.gass.co.id/api/v1/update-data",
		"connectors": []map[string]interface{}{
			{
				"type": "facebook",
				"name": "Gass+LP1",
				"data": map[string]interface{}{
					"pixel_id": "154974637648189",
					"event": map[string]string{
						"impression": "ViewContent",
						"click":      "AddToCart",
						"conversion": "WhatsappClick",
					},
				},
			},
		},
	}

	// Marshal the config to JSON
	jsonData, err := json.Marshal(config)
	if err != nil {
		c.String(http.StatusInternalServerError, "Error generating config")
		return
	}
    // Generate the script content
    // result := fmt.Sprintf("<script>var v = %s;</script>", string(jsonData))
resultData := fmt.Sprintf(bc.RenderView(), string(jsonData),
    string(`!function(f,b,e,v,n,t,s)
  {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
  n.callMethod.apply(n,arguments):n.queue.push(arguments)};
  if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
  n.queue=[];t=b.createElement(e);t.async=!0;
  t.src=v;s=b.getElementsByTagName(e)[0];
  s.parentNode.insertBefore(t,s)}(window, document,'script',
  'https://connect.facebook.net/en_US/fbevents.js');
!function (w, d, t) {
w.TiktokAnalyticsObject=t;var ttq=w[t]=w[t]||[];ttq.methods=["page","track","identify","instances","debug","on","off","once","ready","alias","group","enableCookie","disableCookie"],ttq.setAndDefer=function(t,e){t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}};for(var i=0;i<ttq.methods.length;i++)ttq.setAndDefer(ttq,ttq.methods[i]);ttq.instance=function(t){for(var e=ttq._i[t]||[],n=0;n<ttq.methods.length;n++
)ttq.setAndDefer(e,ttq.methods[n]);return e},ttq.load=function(e,n){var i="https://analytics.tiktok.com/i18n/pixel/events.js";ttq._i=ttq._i||{},ttq._i[e]=[],ttq._i[e]._u=i,ttq._t=ttq._t||{},ttq._t[e]=+new Date,ttq._o=ttq._o||{},ttq._o[e]=n||{};n=document.createElement("script");n.type="text/javascript",n.async=!0,n.src=i+"?sdkid="+e+"&lib="+t;e=document.getElementsByTagName("script")[0];e.parentNode.insertBefore(n,e)};
}(window, document, 'ttq');

var gass=v,url=window.location.href;function getCookie(t){const e='; ${document.cookie}'.split('; ${t}=');if(2===e.length)return e.pop().split(";").shift()}async function post_visit(t,e,i){t.fbp=getCookie("_fbp"),t.fbc=getCookie("_fbc");let a=new FormData;a=appendFormdata(a,t);try{const e=await request_post(t.api_endpoint_url,a);void 0!==t.connector&&Object.keys(t.connector).forEach((e=>{const i=t.connector[e];if("facebook"===i.type){if(fbq&&0===t.send_pixel_fb){t.send_pixel_fb=1,t.fbq=fbq;const e=i.data.pixel_id;t.fbq("init",e,{external_id:t.visitor_id,eventID:"ViewContent-"+t.visitor_id}),t.fbq("track","ViewContent")}}else"tiktok"===i.type&&ttq&&0===t.send_pixel_tt&&(t.send_pixel_tt=1,t.ttq=ttq,t.ttq.load(i.data.pixel_id),t.ttq.page(),t.ttq.track("ViewContent",{user:[{external_id:i.data.visitor_id_hash}],event_id:"ViewContent-"+t.visitor_id}))})),void 0!==i&&i(e)}catch(t){void 0!==i&&i({code:0,msg:"Request failed"})}}function appendFormdata(t,e){e.ip=getCookie("ip_gass"),t.append("page_url",window.location),t.append("domain",e.domain),void 0!==e.ip&&t.append("ip",e.ip),void 0!==typeof e.visitor_id||null!==typeof e.visitor_id?t.append("visitor_id",e.visitor_id):(e.visitor_id=getCookie("visitor_id"),void 0===typeof e.visitor_id&&null===typeof e.visitor_id||t.append("visitor_id",e.visitor_id)),void 0===e.key&&null===e.key||t.append("project_key",e.project_key),void 0!==e.fbc&&void 0!==e.fbc&&null!==e.fbc&&t.append("fbc",e.fbc),void 0!==e.fbp&&void 0!==e.fbp&&null!==e.fbp&&t.append("fbp",e.fbp),void 0!==e._ttp&&void 0!==e._ttp&&null!==e._ttp&&t.append("_ttp",e._ttp),null!==e.ref&&t.append("ref",e.ref);var i=getCookie("client_id");return void 0!==i&&t.append("clientId",i),e.connectors.forEach((function(e){t.append("connector[]",e)})),e.param_get&&Object.keys(e.param_get).forEach((i=>{""!=i&&null!=e.param_get[i]&&t.append(i,e.param_get[i])})),t}async function request_post(t,e){try{const i=await fetch(t,{method:"POST",body:e});if(i.ok){return await i.json()}return{code:0,msg:"Request failed"}}catch(t){return{code:0,msg:"Request Error"}}}async function request_get(t){try{const e=await fetch(t);if(e.ok){return{code:1,msg:await e.text()}}return{code:0,msg:"Request failed"}}catch(t){return{code:0,msg:"Request Error"}}}url=(url=url.replace("https://www.","")).replace("http://www.",""),gass.domain=url.replace("http://","").replace("https://","").split(/[/?#]/)[0],gass.send_pixel_fb=0,gass.send_pixel_tt=0,void 0!==document.referrer&&(gass.ref=document.referrer),window.addEventListener("load",(async function(){try{const t=await request_get("https://ip.gass.co.id/");if(1===t.code){gass.ip=t.gass;const e=1,i="ip_gass",a=t.msg;let o=new Date;o.setTime(o.getTime()+24*e*60*60*1e3);const s="expires="+o.toUTCString();document.cookie=i+"="+a+"; "+s+"; path=/; SameSite=None; Secure"}}catch(t){}gass.timer=null,gass.timer1=null,gass.timer2=null,window.clearInterval(gass.timer),window.clearInterval(gass.timer1),window.clearInterval(gass.timer2);try{await post_visit(gass,"v_update")}catch(t){}const t=6e4,e=2e3;let i=0;gass.timer=window.setInterval((async function(){if(i+=e,gass.fbp=getCookie("_fbp"),void 0!==gass.fbp){try{await post_visit(gass,"v_update")}catch(t){}window.clearInterval(gass.timer)}i>=t&&window.clearInterval(gass.timer)}),e),i=0,gass.timer1=window.setInterval((async function(){if(i+=e,gass.fbc=getCookie("_fbc"),void 0!==gass.fbc){try{await post_visit(gass,"v_update")}catch(t){}window.clearInterval(gass.timer1)}i>=t&&window.clearInterval(gass.timer1)}),e),i=0,gass.timer2=window.setInterval((async function(){i+=e;const a=getCookie("_ttp");if(void 0!==a){gass._ttp=a;try{await post_visit(gass,"v_update")}catch(t){}window.clearInterval(gass.timer2)}i>=t&&window.clearInterval(gass.timer2)}),e)}));`))
    // Set the response headers and body
    c.Header("Content-Type", "text/html")
    c.String(http.StatusOK, resultData)
}


func (bc *BrochureController) CountAndConvertVisitorId(c *gin.Context) {
	visitorID := "12.1.1.3123"
	result := bc.getConv(visitorID)

	// Set the response headers and body
	c.Header("Content-Type", "text/html")
	c.String(http.StatusOK, result)
}

func (bc *BrochureController) getConv(visitorID string) string  {
	fmt.Println("visitor id", visitorID)

	// Convert visitorID
	shortVisitorID := convBase(visitorID, ".0123456789", "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ")
	shortVisitorID = addDot(shortVisitorID, 4)
	fmt.Println("short visitor id", shortVisitorID)

	// Reverse visitorID back
	revVisitorID := strings.Replace(shortVisitorID, ".", "", -1)
	revVisitorID = convBase(revVisitorID, "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", ".0123456789")
	fmt.Println("reverse", revVisitorID)

	// Return the final converted result
	return shortVisitorID
}

func convBase(number, fromBase, toBase string) string {
	if fromBase == toBase {
		return number
	}

	fromBaseSlice := []rune(fromBase)
	toBaseSlice := []rune(toBase)
	numberSlice := []rune(number)
	fromLen := len(fromBaseSlice)
	toLen := len(toBaseSlice)
	numberLen := len(numberSlice)

	// Convert from 'fromBase' to base 10
	retval := ""
	if toBase == "0123456789" {
		retval = "0"
		for i := 1; i <= numberLen; i++ {
			multiplier := big.NewInt(int64(fromLen))
			multiplier.Exp(multiplier, big.NewInt(int64(numberLen-i)), nil)

			// Handle invalid characters in the number string
			charIndex := strings.IndexRune(fromBase, numberSlice[numberLen-i])
			if charIndex == -1 {
				// Return an error or handle appropriately
				return "Error: Invalid character in the input number"
			}

			addend := big.NewInt(int64(charIndex))
			addend.Mul(addend, multiplier)
			sum := big.NewInt(0)
			retvalBigInt, success := big.NewInt(0).SetString(retval, 10)
			if !success {
				// Handle the error if retval cannot be parsed as an integer
				fmt.Println("Error: Invalid number format for retval")
				return "Error"
			}
			sum.Add(retvalBigInt, addend)

			retval = sum.String()
		}
		return retval
	}

	// Convert from base 'fromBase' to base 10 (if needed)
	base10 := ""
	if fromBase != "0123456789" {
		base10 = convBase(number, fromBase, "0123456789")
	} else {
		base10 = number
	}

	// Convert base 10 to 'toBase'
	if num, err := strconv.ParseInt(base10, 10, 64); err == nil && num < int64(toLen) {
		return string(toBaseSlice[num])
	}

	for num, err := strconv.ParseInt(base10, 10, 64); err == nil && num != 0; num = num / int64(toLen) {
		mod := num % int64(toLen)
		retval = string(toBaseSlice[mod]) + retval
	}

	return retval
}

func addDot(s string, n int) string {
	for i := len(s) - n; i > 0; i -= n {
		s = s[:i] + "." + s[i:]
	}
	return s
}

func (bc *BrochureController) SendData(c *gin.Context) {
	eventName := "ViewContent"
	eventTime := time.Now().Unix() // Current timestamp
	eventID := "123456789"
	email := "<EMAIL>"
	phone := "1234567890"
	firstName := "John"
	lastName := "Doe"
	gender := "m"
	birthDate := "19900101"
	city := "Jakarta"
	state := "JK"
	zip := "12345"
	country := "ID"
	currency := "IDR"
	value := 100000.0
	eventSourceURL := "https://www.example.com/product-page"
	actionSource := "website"
	accessToken := "EAAMT87ZBxiwIBO2FV2gNUKevI6gsxMpIg5XoTU55gK7ZBsqJYjigvWBiQqmFZCiQxfmnQsZAUCjflUZBfod55jpFiun5BbEmW8QRWDS9X8DAA8DyKV3Vqz6y7jC8Lj0PVIxK9TKFPCYrEH9oN65XbbkVAZC0gZBk5ZAA7AYhGiXoc6ABoYkjk57HVnd1W1ouoDN9qgZDZD"
	pixelId := "2396749237041633"
	fbp := c.Param("fbp")
	domain := c.Param("domain")
	ip := c.Param("ip")
	pageURL := c.Param("page_url")
	ref := c.Param("ref")
	projectKey := c.Param("project_key")
	visitorID := c.Param("visitor_id")
	resp := services.SendData(eventName, eventTime, eventID, email, phone, firstName, lastName, gender, birthDate, city, state, zip, country, currency, value, eventSourceURL, actionSource, accessToken, domain, fbp, ip, pageURL, ref, projectKey, visitorID, pixelId)
	if resp == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send data to Facebook API"})
		return
	}

	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to read response body"})
		return
	}

	if resp.StatusCode != http.StatusOK {
		c.JSON(resp.StatusCode, gin.H{"error": string(body)})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Event sent successfully", "response": string(body)})
}




func (bc *BrochureController) RenderBrochure(c *gin.Context) {
	urlParam := c.Param("url")
	rotator, err := bc.repoRotator.GetByShortURL(urlParam)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve RotatorURL"})
		return
	}
	if rotator == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "RotatorURL not found"})
		return
	}
	rotator.Impression++
	if err := bc.repoRotator.Update(rotator); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update RotatorURL"})
		return
	}

	brochures, err := bc.repo.GetByRotator(rotator.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	if len(brochures) == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "No brochures found for this RotatorURL"})
		return
	}
	
	var view *models.Brochure
	var visitorID string
	var createdVisitor uint
	var qrCode []byte
	var randQr string
	var qrCodeID uint
	var qrCodeMessage string
	cookieVisitorID, err := c.Cookie("visitor_id")
	log.Println(cookieVisitorID)
	var qrCodeFile string
	if err != nil {
		
		view = bc.BestBrochureEpsilonGreedy(brochures, 0.1)
		visitor := &models.Visitor{RotatorURLID: rotator.ID, BrochureID: view.ID}
		createdVisitor, err = bc.repoVisitor.Create(*visitor)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		// log.Println("before hash" +strconv.FormatUint(uint64(rotator.ID), 10) + "." + strconv.FormatUint(uint64(view.ID), 10) + ".1."+ strconv.FormatUint(uint64(createdVisitor), 10))
		visitorID = bc.getConv(strconv.FormatUint(uint64(rotator.ID), 10) + "." + strconv.FormatUint(uint64(view.ID), 10) + ".1."+ strconv.FormatUint(uint64(createdVisitor), 10))
		// log.Println("after hash" +visitorID)
		// log.Println("before if 1"+visitorID)
		
		randQr = visitorID
		// log.Println("randQr"+visitorID)
		qrCode, err = qrcode.Encode(randQr, qrcode.Medium, 256)
		if err != nil {
			c.String(http.StatusInternalServerError, fmt.Sprintf("Error generating QR code: %s", err))
			return
		}
		
		visitorUpdated := models.Visitor{
			ID:         createdVisitor,
			QRCode:     randQr,
			VisitorID:  visitorID,
			BrochureID: view.ID,
			RotatorURLID: rotator.ID,
		}
		view.Impressions++
		err = bc.repo.Update(view)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		err2 := bc.repoVisitor.Update(visitorUpdated)
		if err2 != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		
		var visitorProfile models.VisitorProfile
		visitorProfile.IP = c.ClientIP()
		visitorProfile.BrowserAgent = c.Request.UserAgent()
		visitorProfile.Referral = c.Request.Referer()
		visitorProfile.VisitorID = visitorID

		if err := c.Bind(&visitorProfile); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		if bc.repoVisitorProfile == nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "repository is nil"})
			return
		}

		if err := bc.repoVisitorProfile.Create(&visitorProfile); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		defaultMessage := "Hi, saya ingin mendapatkan brosur Gass LP1 "+randQr
		caption := "Thanks "
		qrCodeFile = fmt.Sprintf("qr-codes/%s.png", randQr)
		qrCodeCreated := models.QRCode{
			CodeFile:       qrCodeFile,
			Code:       randQr,
			VisitorProfileID: visitorProfile.ID,
			BrochureID: view.ID,
			DefaultMessage : defaultMessage,
			Caption : caption,
		}
		
		err = bc.repoQrCode.Create(&qrCodeCreated)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		qrCodeID = qrCodeCreated.ID
		qrCodeMessage = qrCodeCreated.DefaultMessage

	}else{
		visitorID = cookieVisitorID
		log.Println("new visitor else"+visitorID)
		createdVisitor, err := bc.repoVisitor.GetByVisitorID(visitorID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		if len(createdVisitor) == 0 {
			c.JSON(http.StatusNotFound, gin.H{"error": "Visitor not found"})
			return
		}
		view, err = bc.repo.GetBrochureBodyByID(createdVisitor[0].BrochureID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		log.Println("before if 2"+visitorID)
		randQr = visitorID
		log.Println("randQr"+visitorID)
		qrCode, err = qrcode.Encode(randQr, qrcode.Medium, 256)
		if err != nil {
			c.String(http.StatusInternalServerError, fmt.Sprintf("Error generating QR code: %s", err))
			return
		}
		qrCodeFromDB, err := bc.repoQrCode.GetByCode(randQr)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		if qrCodeFromDB == nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "QRCode not found"})
			return
		}
		qrCodeID = qrCodeFromDB.ID
		qrCodeFile = qrCodeFromDB.CodeFile
		qrCodeMessage = qrCodeFromDB.DefaultMessage
	}
	// log.Println("visitorID inside if else"+vixsitorID)
	// randString := fmt.Sprintf("%d", rand.Int())
	// cookieVisitorID, err := c.Cookie("visitor_id")
	// if err != nil {
	// 	visitorID = visitorID
	// } else {
	// 	visitorID = cookieVisitorID
	// }

	config := map[string]interface{}{
		"visitor_id":       visitorID,
		"project_key":      "DcZKCu",
		"api_endpoint_url": "https://api-ebrosur.gass.co.id/api/v1/update-data",
		"connectors": []map[string]interface{}{
			{
				"type": "facebook",
				"name": "Gass+LP1",
				"data": map[string]interface{}{
					"pixel_id": "154974637648189",
					"event": map[string]string{
						"impression": "ViewContent",
						"click":      "AddToCart",
						"conversion": "WhatsappClick",
					},
				},
			},
		},
	}

	jsonData, err := json.Marshal(config)
	if err != nil {
		c.String(http.StatusInternalServerError, "Error generating config")
		return
	}


	// Ensure the directory exists
	if err := os.MkdirAll("qr-codes", os.ModePerm); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Error creating directory for QR codes",
			"details": err.Error(),
		})
		return
	}

	// Save the QR code to the file
	if err := os.WriteFile(qrCodeFile, qrCode, 0644); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Error saving QR code to file",
			"details": err.Error(),
		})
		return
	}

	log.Println("visitorid "+visitorID)
	c.SetCookie("visitor_id", visitorID, 3600*24*30, "/", "localhost:8010", true, true)
	c.SetSameSite(http.SameSiteNoneMode)



	viewBody := view.Body
	// log.Println(view)
	// Regular expression to match the content inside <section class="visitor-qrcode">...</section>
	re := regexp.MustCompile(`(?s)(<section class="visitor-qrcode">)(.*?)(</section>)`)

	// Replace the content inside the <section> tags
	updatedHTML := re.ReplaceAllString(viewBody, `$1`+fmt.Sprintf("<img src=\"/qr-codes/%s.png\"><input type=\"hidden\" name=\"id_visitor\" value=\"%s\">", randQr, createdVisitor)+`$3`)

	// Replace the form action
	updatedHTML = strings.Replace(updatedHTML, `<form method="POST" action="#" id="form-qrcode" class="form-qrcode">`, `<form method="POST" action="http://`+c.Request.Host+`/receive-form-qr" id="form-qrcode" class="form-qrcode"><input type="hidden" name="id_visitor" value="`+strconv.Itoa(int(createdVisitor))+`">`, 1)
	
	
	updatedHTML = strings.Replace(updatedHTML, `<form method="GET" action="#" id="button-qrcode" class="button-qrcode">`, `<form method="GET" action="http://`+c.Request.Host+`/store-loc/`+strconv.FormatUint(uint64(qrCodeID), 10)+`" id="button-qrcode" class="button-qrcode">`, 1)
	
	// updatedHTML = strings.Replace(updatedHTML, `<a id="ih3l">`, `<a id="ih3l" href="http://`+c.Request.Host+`/store-loc/`+strconv.FormatUint(uint64(qrCodeID), 10)+`">`, 1)
	updatedHTML = strings.Replace(updatedHTML, `<a id="ih3l">`, `<a id="ih3l" href="https://wa.me/6283822315603?text=`+url.QueryEscape(qrCodeMessage)+`">`, 1)
	log.Println("test qr",randQr)
	
	updatedHTML = strings.Replace(updatedHTML, `<form method="GET" action="#" id="button-send-whatsapp" class="button-send-whatsapp">`, `<form method="POST" action="http://`+c.Request.Host+`/receive-whatsapp-qr" id="button-send-whatsapp" class="button-send-whatsapp"><input type="hidden" data-layerable="false" name="code" value="`+randQr+`">`, 1)
	// updatedHTML = strings.Replace(updatedHTML, `<input type="hidden" data-layerable="false" name="default_message" `, `<input type="hidden" data-layerable="false" name="text" `, 1)
	// updatedHTML = strings.Replace(updatedHTML, `<input type="hidden" data-layerable="false" name="no_whatsapp" `, `<input type="hidden" data-layerable="false" name="phone" `, 1)
	updatedHTML = strings.Replace(updatedHTML, `%ID%`, randQr, 1)


	data := struct {
		Json     template.JS
		Brochure template.HTML
	}{
		Json:     template.JS(string(jsonData)),
		Brochure: template.HTML(updatedHTML),
	}

	// log.Println(updatedHTML)

	// Load the HTML template
	tmpl, err := template.ParseFiles("views/brochure.html")
	if err != nil {
		c.String(http.StatusInternalServerError, "Unable to load template")
		return
	}

	// Render the template with the provided data
	err = tmpl.Execute(c.Writer, data)
	if err != nil {
		c.String(http.StatusInternalServerError, "Unable to render template")
	}
}

func (bc *BrochureController) UpdateData(c *gin.Context) {
    // Step 1: Parse the form data
    if err := c.Request.ParseMultipartForm(32 << 20); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse form data"})
        return
    }

    formData := c.Request.Form

    // Convert the form data to JSON
    bodyJSON, err := json.Marshal(formData)
	log.Println(bodyJSON)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to convert form data to JSON"})
        return
    }
	context := gin.Context{}
	context.Set("email", "")
	context.Set("phone", "")
	context.Set("first_name", "")
	context.Set("last_name", "")
	context.Set("gender", "")
	context.Set("birth_date", "")
	context.Set("city", "")
	context.Set("state", "")
	context.Set("zip", "")
	context.Set("country", "")
	context.Set("currency", "")
	context.Set("value", "")
	context.Set("fbp", formData.Get("fbp"))
	context.Set("domain", formData.Get("domain"))
	context.Set("ip", formData.Get("ip"))
	context.Set("page_url", formData.Get("page_url"))
	context.Set("ref", formData.Get("ref"))
	context.Set("project_key", formData.Get("project_key"))
	context.Set("visitor_id", formData.Get("visitor_id"))
	// Pass the context to the SendDataViewContent function
	bc.SendDataViewContent(&context)
	
	log.Println("Terjadi error atau result nil")
    // Step 2: Save the JSON to the Payload model
    payload := models.Payload{
        Body: string(bodyJSON), // Store the JSON string in the Body column
    }

    // Save the payload using your repository method
    if err := bc.repoPayload.Create(&payload); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update payload"})
        return
    }

    c.JSON(http.StatusOK, gin.H{"message": "Payload updated successfully"})
}
func (bc *BrochureController) getUser(c *gin.Context) (*models.User, error) {
	// Get user ID from the context
	currentUser, exists := c.Get("userKey")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User ID not found in context"})
		return nil, fmt.Errorf("User ID not found in context")
	}

	// Retrieve the user from the repository
	user, err := bc.repoUser.GetByUserKey(currentUser.(string))
	if err != nil {
		return nil, fmt.Errorf("Failed to retrieve user project key")
	}
	if user == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return nil, fmt.Errorf("User not found")
	}
	if user.ProjectKey == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User project key is required"})
		return nil, fmt.Errorf("User project key is required")
	}

	return user, nil
}


func (bc *BrochureController) ScanHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "qrcode success redeem"})
}

func (bc *BrochureController) BestBrochureEpsilonGreedy(brochures []models.Brochure, epsilon float64) *models.Brochure {
	if len(brochures) == 0 {
		return nil
	}

	bestBrochure := &brochures[0]
	maxConversionRate := 0.0

	for _, brochure := range brochures {
		conversionRate := rand.Float64() // Assume some conversion rate for each brochure

		if conversionRate > maxConversionRate || rand.Float64() < epsilon {
			bestBrochure = &brochure
			maxConversionRate = conversionRate
		}
	}

	return bestBrochure
}

func (bc *BrochureController) SendDataViewContent(c *gin.Context) {
	eventName := "ViewContent"
	eventTime := time.Now().Unix() // Current timestamp
	eventID := fmt.Sprintf("%s_%s", eventName, c.MustGet("visitor_id"))
	email, _ := c.Get("email")
	phone, _ := c.Get("phone")
	firstName, _ := c.Get("first_name")
	lastName, _ := c.Get("last_name")
	gender, _ := c.Get("gender")
	birthDate, _ := c.Get("birth_date")
	city, _ := c.Get("city")
	state, _ := c.Get("state")
	zip, _ := c.Get("zip")
	country, _ := c.Get("country")
	currency, _ := c.Get("currency")
	valueStr, _ := c.Get("value")
	value, _ := strconv.ParseFloat(valueStr.(string), 64)
	eventSourceURL := "https://www.example.com/product-page"
	actionSource := "website"
	accessToken := "EAAMT87ZBxiwIBO2FV2gNUKevI6gsxMpIg5XoTU55gK7ZBsqJYjigvWBiQqmFZCiQxfmnQsZAUCjflUZBfod55jpFiun5BbEmW8QRWDS9X8DAA8DyKV3Vqz6y7jC8Lj0PVIxK9TKFPCYrEH9oN65XbbkVAZC0gZBk5ZAA7AYhGiXoc6ABoYkjk57HVnd1W1ouoDN9qgZDZD"
	pixelId := "2396749237041633"
	fbp, _ := c.Get("fbp")
	domain, _ := c.Get("domain")
	ip, _ := c.Get("ip")
	pageURL, _ := c.Get("page_url")
	ref, _ := c.Get("ref")
	projectKey, _ := c.Get("project_key")
	visitorID, _ := c.Get("visitor_id")
	log.Println("test console" + fbp.(string))
	resp := services.SendData(eventName, eventTime, eventID, email.(string), phone.(string), firstName.(string), lastName.(string), gender.(string), birthDate.(string), city.(string), state.(string), zip.(string), country.(string), currency.(string), value, eventSourceURL, actionSource, accessToken, domain.(string), fbp.(string), ip.(string), pageURL.(string), ref.(string), projectKey.(string), visitorID.(string), pixelId)
	if resp == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send data to Facebook API"})
		return
	}

	defer resp.Body.Close()

	// body, err := io.ReadAll(resp.Body)
	// if err != nil {
	// 	c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to read response body"})
	// 	return
	// }

	return 

}

func (bc *BrochureController) PreviewBrochure(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid brochure ID"})
		return
	}

	brochure, err := bc.repo.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve brochure"})
		return
	}
	if brochure == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Brochure not found"})
		return
	}

	if brochure.Body == "" {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Brochure body is empty"})
		return
	}

	data := struct {
		Brochure template.HTML
	}{
		Brochure: template.HTML(brochure.Body),
	}
	
	// Load the HTML template
	tmpl, err := template.ParseFiles("views/brochure_preview.html")
	if err != nil {
		c.String(http.StatusInternalServerError, "Unable to load template")
		return
	}
	// Render the template with the provided data
	err = tmpl.Execute(c.Writer, data)
	if err != nil {
		c.String(http.StatusInternalServerError, "Unable to render template")
	}
}
