package controllers

import (
	"net/http"
	"strconv"

	"e-brochure/models"
	"e-brochure/repositories"

	"github.com/gin-gonic/gin"
)

// QRCodeController handles qrcode-related HTTP requests
type QRCodeController struct {
	qrCodeRepo repositories.QrCodeRepository
}

// NewQRCodeController creates a new QRCodeController with the given repository
func NewQRCodeController(qrCodeRepo repositories.QrCodeRepository) *QRCodeController {
	return &QRCodeController{qrCodeRepo: qrCodeRepo}
}

// CreateQRCode creates a new QRCode
func (qcc *QRCodeController) CreateQRCode(c *gin.Context) {
	var qrCode models.QRCode
	if err := c.Bind(&qrCode); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := qcc.qrCodeRepo.Create(&qrCode); err != nil {
		c.J<PERSON>(http.StatusInternalServerError, gin.H{"error": err.<PERSON>rror()})
		return
	}

	c.JSON(http.StatusCreated, qrCode)
}

// GetAllQRCodes returns all QRCodes
func (qcc *QRCodeController) GetAllQRCodes(c *gin.Context) {
	qrcodes, err := qcc.qrCodeRepo.GetAll()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, qrcodes)
}

// GetQRCodeByID returns a QRCode by ID
func (qcc *QRCodeController) GetQRCodeByID(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	qrcode, err := qcc.qrCodeRepo.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if qrcode == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "QRCode not found"})
		return
	}

	c.JSON(http.StatusOK, qrcode)
}

// UpdateQRCode updates a QRCode
func (qcc *QRCodeController) UpdateQRCode(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid QRCode ID"})
		return
	}

	var qrCode models.QRCode
	if err := c.ShouldBindJSON(&qrCode); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input"})
		return
	}

	qrCode.ID = uint(id) // Ensure the ID is set from the URL
	if err := qcc.qrCodeRepo.Update(&qrCode); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update QRCode"})
		return
	}

	c.JSON(http.StatusOK, qrCode)
}

// DeleteQRCode deletes a QRCode
func (qcc *QRCodeController) DeleteQRCode(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := qcc.qrCodeRepo.Delete(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusNoContent, nil)
}
