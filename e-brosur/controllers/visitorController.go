package controllers

import (
	"e-brochure/models"
	"e-brochure/repositories"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type VisitorController struct {
    visitorRepo repositories.VisitorRepository
}

func NewVisitorController(r repositories.VisitorRepository) *VisitorController {
    return &VisitorController{visitorRepo: r}
}

func (ctrl *VisitorController) GetAllVisitors(c *gin.Context) {
    visitors, err := ctrl.visitorRepo.FindAll()
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    c.<PERSON>(http.StatusOK, visitors)
}

func (ctrl *VisitorController) GetVisitorByID(c *gin.Context) {
    id, err := strconv.Atoi(c.<PERSON>m("id"))
    if err != nil {
        c.<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
        return
    }

    visitor, err := ctrl.visitorRepo.FindByID(uint(id))
    if err != nil {
        c.JSON(http.StatusNotFound, gin.H{"error": "Visitor not found"})
        return
    }

    c.JSON(http.StatusOK, visitor)
}

func (ctrl *VisitorController) CreateVisitor(c *gin.Context) {
    var visitor models.Visitor
    if err := c.ShouldBindJSON(&visitor); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    _, err := ctrl.visitorRepo.Create(visitor)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusCreated, visitor)
}

func (ctrl *VisitorController) UpdateVisitor(c *gin.Context) {
    id, err := strconv.Atoi(c.Param("id"))
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
        return
    }

    var visitor models.Visitor
    if err := c.ShouldBindJSON(&visitor); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    visitor.ID = uint(id) // Set the ID from the path
    if err := ctrl.visitorRepo.Update(visitor); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusOK, visitor)
}

func (ctrl *VisitorController) DeleteVisitor(c *gin.Context) {
    id, err := strconv.Atoi(c.Param("id"))
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
        return
    }

    if err := ctrl.visitorRepo.Delete(uint(id)); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.Status(http.StatusNoContent)
}
