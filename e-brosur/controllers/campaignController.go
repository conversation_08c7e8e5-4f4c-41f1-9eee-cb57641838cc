package controllers

import (
	"net/http"
	"strconv"

	"e-brochure/models"
	"e-brochure/repositories"

	"github.com/gin-gonic/gin"
)

// CampaignController handles campaign-related HTTP requests
type CampaignController struct {
	repo repositories.CampaignRepository
	repoInfluecer repositories.InfluencerRepository
}

// NewCampaignController creates a new CampaignController
func NewCampaignController(repo repositories.CampaignRepository, repoInfluecer repositories.InfluencerRepository) *CampaignController {
	return &CampaignController{
		repo: repo,
		repoInfluecer: repoInfluecer,
	}
}

// CreateCampaign creates a new campaign
func (cc *CampaignController) CreateCampaign(c *gin.Context) {
	var campaign models.Campaign
	if err := c.ShouldBindJSON(&campaign); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := cc.repo.Create(&campaign); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create campaign"})
		return
	}

	c.JSON(http.StatusCreated, campaign)
}

// GetCampaign retrieves a campaign by ID
func (cc *CampaignController) GetCampaign(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid campaign ID"})
		return
	}

	campaign, err := cc.repo.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve campaign"})
		return
	}

	c.JSON(http.StatusOK, campaign)
}

// UpdateCampaign updates an existing campaign
func (cc *CampaignController) UpdateCampaign(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid campaign ID"})
		return
	}

	var campaign models.Campaign
	if err := c.ShouldBindJSON(&campaign); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	campaign.ID = uint(id)

	if err := cc.repo.Update(&campaign); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update campaign"})
		return
	}

	c.JSON(http.StatusOK, campaign)
}

// DeleteCampaign deletes a campaign by ID
func (cc *CampaignController) DeleteCampaign(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid campaign ID"})
		return
	}

	if err := cc.repo.Delete(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete campaign"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Campaign deleted successfully"})
}

// GetAllCampaigns retrieves all campaigns
func (cc *CampaignController) GetAllCampaigns(c *gin.Context) {
	campaigns, err := cc.repo.GetAll()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve campaigns", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, campaigns)
}
// AssignInfluencer assigns an influencer to a campaign
func (cc *CampaignController) AssignInfluencer(c *gin.Context) {
	var request struct {
		CampaignID   uint `json:"campaign_id"`
		InfluencerID uint `json:"influencer_id"`
	}
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	campaign, err := cc.repo.GetByID(request.CampaignID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get campaign"})
		return
	}

	influencer, err := cc.repoInfluecer.GetByID(request.InfluencerID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get influencer"})
		return
	}

	if err := cc.repo.AssignInfluencerToCampaign(campaign, influencer); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to assign influencer"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Influencer assigned successfully"})

}

