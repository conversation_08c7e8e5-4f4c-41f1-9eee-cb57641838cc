package controllers

import (
	"e-brochure/repositories"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)
type epsilonGreedyController struct {
	repo repositories.RotatorURLRepository
}
// type epsilonGreedyController struct {
// 	repo repositories.BrochureRepository
// }

// Define the probabilities for each landing page
// var probabilities = []float64{0.4, 0.4, 0.2}

// SelectLandingPage chooses a landing page based on predefined probabilities
// func SelectLandingPage() *epsilonGreedyController {
// 	r := rand.Float64()
// 	cumulativeProbability := 0.0

// 	for i, prob := range probabilities {
// 		cumulativeProbability += prob
// 		if r < cumulativeProbability {
// 			return pages[i]
// 		}
// 	}

// 	// Fallback to the first page (just in case)
// 	return pages[0]
// }

// // Simulate button click, register the event
// func ButtonClick(page *LandingPage) {
// 	page.Clicks++
// }

// // ShowPage simulates showing the page and tracks impressions
// func ShowPage() *LandingPage {
// 	page := SelectLandingPage()
// 	page.Impressions++
// 	fmt.Printf("Showing Landing Page %d\n", page.ID)
// 	return page
// }

// func main() {
// 	rand.Seed(time.Now().UnixNano())

// 	// Simulate 1000 visits to the site
// 	for i := 0; i < 1000; i++ {
// 		page := ShowPage()

// 		// Simulate button click with a 10% chance
// 		if rand.Float64() < 0.1 {
// 			ButtonClick(page)
// 		}
// 	}

// 	// Print the results
// 	for _, page := range pages {
// 		fmt.Printf("Landing Page %d: Impressions = %d, Clicks = %d, Conversion Rate = %.2f%%\n",
// 			page.ID, page.Impressions, page.Clicks, float64(page.Clicks)/float64(page.Impressions)*100)
// 	}
// }

func (rc *epsilonGreedyController) GetRotatorURL(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid RotatorURL ID"})
		return
	}

	rotator, err := rc.repo.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve RotatorURL"})
		return
	}
	if rotator == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "RotatorURL not found"})
		return
	}
	
	rotator.Click = rotator.Click + 1

	c.JSON(http.StatusOK, rotator)
}
