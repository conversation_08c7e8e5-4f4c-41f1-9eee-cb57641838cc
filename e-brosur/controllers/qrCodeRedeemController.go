package controllers

import (
	"fmt"
	"net/http"
	"strconv"

	"e-brochure/models"
	"e-brochure/repositories"

	"github.com/gin-gonic/gin"
)

// QRCodeRedeemController handles qr_code_redeem-related HTTP requests
type QRCodeRedeemController struct {
	qrCodeRedeemRepo repositories.QRCodeRedeemRepository
	qrCodeRepo repositories.QrCodeRepository
	visitorProfileRepo repositories.VisitorProfileRepository
	repoUser repositories.UserRepository
}

// NewQRCodeRedeemController creates a new QRCodeRedeemController with the given repository
func NewQRCodeRedeemController(qrCodeRedeemRepo repositories.QRCodeRedeemRepository, qrCodeRepo repositories.QrCodeRepository, visitorProfileRepo repositories.VisitorProfileRepository, repoUser repositories.UserRepository) *QRCodeRedeemController {
	return &QRCodeRedeemController{qrCodeRedeemRepo: qrCodeRedeemRepo, 
		visitorProfileRepo: visitorProfileRepo,
		qrCodeRepo: qrCodeRepo,
		repoUser: repoUser,}
}

// CreateQRCodeRedeem creates a new QRCodeRedeem
func (qcrc *QRCodeRedeemController) CreateQRCodeRedeem(c *gin.Context) {
	var qrCodeRedeem models.QRCodeRedeem
	if err := c.Bind(&qrCodeRedeem); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := qcrc.qrCodeRedeemRepo.Create(&qrCodeRedeem); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, qrCodeRedeem)
}

// GetQRCodeRedeemByID returns a QRCodeRedeem by ID
func (qcrc *QRCodeRedeemController) GetQRCodeRedeemByID(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	qrCodeRedeem, err := qcrc.qrCodeRedeemRepo.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if qrCodeRedeem == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "QRCodeRedeem not found"})
		return
	}

	c.JSON(http.StatusOK, qrCodeRedeem)
}

// GetQRCodeRedeemsByQRCodeID returns all QRCodeRedeems by QRCodeID
func (qcrc *QRCodeRedeemController) GetQRCodeRedeemsByQRCodeID(c *gin.Context) {
	qrCodeID, err := strconv.Atoi(c.Param("qrCodeID"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	qrCodeRedeems, err := qcrc.qrCodeRedeemRepo.GetByQRCodeID(uint(qrCodeID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if qrCodeRedeems == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "QRCodeRedeems not found"})
		return
	}

	c.JSON(http.StatusOK, qrCodeRedeems)
}

// GetQRCodeRedeemsByVisitorID returns all QRCodeRedeems by VisitorID
func (qcrc *QRCodeRedeemController) GetQRCodeRedeemsByVisitorID(c *gin.Context) {
	visitorID, err := strconv.Atoi(c.Param("visitorID"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	qrCodeRedeems, err := qcrc.qrCodeRedeemRepo.GetByVisitorID(uint(visitorID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if qrCodeRedeems == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "QRCodeRedeems not found"})
		return
	}

	c.JSON(http.StatusOK, qrCodeRedeems)
}

// UpdateQRCodeRedeem updates a QRCodeRedeem
func (qcrc *QRCodeRedeemController) UpdateQRCodeRedeem(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var qrCodeRedeem models.QRCodeRedeem
	if err := c.Bind(&qrCodeRedeem); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	qrCodeRedeem.ID = uint(id)

	if err := qcrc.qrCodeRedeemRepo.Update(&qrCodeRedeem); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update QRCodeRedeem"})
		return
	}

	c.JSON(http.StatusOK, qrCodeRedeem)
}

// DeleteQRCodeRedeem deletes a QRCodeRedeem by ID
func (qcrc *QRCodeRedeemController) DeleteQRCodeRedeem(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid QRCodeRedeem ID"})
		return
	}

	if err := qcrc.qrCodeRedeemRepo.Delete(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete QRCodeRedeem"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "QRCodeRedeem deleted successfully"})
}

// GetAllQRCodeRedeems returns all QRCodeRedeems
func (qcrc *QRCodeRedeemController) GetAllQRCodeRedeems(c *gin.Context) {
	user, err := qcrc.getUser(c)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user", "details": err.Error()})
		return
	}
	
	qrCodeRedeems, err := qcrc.qrCodeRedeemRepo.GetByStoreID(*user.StoreID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve QRCodeRedeem", "details": err.Error()})
		return
	}
	c.JSON(http.StatusOK, qrCodeRedeems)
}
func (qcrc *QRCodeRedeemController) RedeemQRCodeByCode(c *gin.Context) {
	var jsonData struct {
		Code   string `json:"code"`
		Amount uint   `json:"amount"`
		Phone  string `json:"phone"`
		StoreID uint   `json:"store_id"`
	}

	if err := c.ShouldBindJSON(&jsonData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input"})
		return
	}

	code := jsonData.Code
	qrCode, err := qcrc.qrCodeRepo.GetByCode(code)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if qrCode == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "QRCode not found"})
		return
	}

	// Check if QRCodeID is already redeemed
	redeems, err := qcrc.qrCodeRedeemRepo.GetByQRCodeID(qrCode.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check QRCode redeems"})
		return
	}

	if len(redeems) > 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "your QRCode already redeemed"})
		return
	}

	visitorProfile, err := qcrc.visitorProfileRepo.GetByID(qrCode.VisitorProfileID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get VisitorProfile"})
		return
	}

	qrCodeRedeem := models.QRCodeRedeem{
		QRCodeID:     qrCode.ID,
		VisitorID:    visitorProfile.ID,
		Amount:       jsonData.Amount,
		Phone:        jsonData.Phone,
		StoreID:      jsonData.StoreID,
		IP:           visitorProfile.IP,
		BrowserAgent: visitorProfile.BrowserAgent,
		Referral:     visitorProfile.Referral,
	}

	if err := qcrc.qrCodeRedeemRepo.Create(&qrCodeRedeem); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create QRCodeRedeem"})
		return
	}

	c.JSON(http.StatusCreated, qrCodeRedeem)
}

func (qcrc *QRCodeRedeemController) getUser(c *gin.Context) (*models.User, error) {
	// Get user ID from the context
	currentUser, exists := c.Get("userKey")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User ID not found in context"})
		return nil, fmt.Errorf("User ID not found in context")
	}

	// Retrieve the user from the repository
	user, err := qcrc.repoUser.GetByUserKey(currentUser.(string))
	if err != nil {
		return nil, fmt.Errorf("Failed to retrieve user project key")
	}
	if user == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return nil, fmt.Errorf("User not found")
	}
	if user.ProjectKey == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User project key is required"})
		return nil, fmt.Errorf("User project key is required")
	}
	if user.StoreID == nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User store id is required"})
		return nil, fmt.Errorf("User store id is required")
	}

	return user, nil
}