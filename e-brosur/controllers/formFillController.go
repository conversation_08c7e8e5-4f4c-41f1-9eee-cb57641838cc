package controllers

import (
	"database/sql"
	"fmt"
	"html/template"
	"log"
	"net/http"
	"net/url"
	"strconv"

	"e-brochure/models"
	"e-brochure/repositories"

	"github.com/gin-gonic/gin"
)

// FormFillController handles form fill-related HTTP requests
type FormFillController struct {
	repo repositories.FormFillRepository
	visitorRepo repositories.VisitorRepository
	repoQrCode repositories.QrCodeRepository
	storeRepo repositories.StoreRepository
}

// NewFormFillController creates a new instance of FormFillController
func NewFormFillController(repo repositories.FormFillRepository, visitorRepo repositories.VisitorRepository, repoQrCode repositories.QrCodeRepository, storeRepo repositories.StoreRepository) *FormFillController {
	return &FormFillController{repo: repo, visitorRepo: visitorRepo, repoQrCode: repoQrCode, storeRepo: storeRepo}
}

// List returns all form fill data
func (ctrl *FormFillController) List(c *gin.Context) {
	formFills, err := ctrl.repo.GetAll()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, formFills)
}

// GetById returns a form fill by id
func (ctrl *FormFillController) GetById(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	formFill, err := ctrl.repo.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	if formFill == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Form fill not found"})
		return
	}

	c.JSON(http.StatusOK, formFill)
}

// CreateFromForm creates a new form fill from form data
func (ctrl *FormFillController) Create(c *gin.Context) {
	firstName := c.PostForm("firstname")
	if firstName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "First name is required"})
		return
	}
	phone := c.PostForm("phone")
	if phone == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Phone is required"})
		return
	}
	formFill := models.FormFill{
		FirstName: firstName,
		Phone: phone,
	}
	visitorID := c.PostForm("id_visitor")
	if visitorID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Visitor ID is required"})
		return
	}

	id, err := strconv.Atoi(visitorID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid Visitor ID"})
		return
	}

	visitor, err := ctrl.visitorRepo.FindByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	_, err = ctrl.repo.Create(&formFill)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	qrCode, err := ctrl.repoQrCode.GetByCode(visitor.QRCode)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	// var url string
	// url = "http://" + c.Request.URL.String()
	// _, err = services.EbrosurAddMessage("6283822315603", "6282110799347", "Terimakasih telah melakukan pengisian form", "text", url)
	// if err != nil {
	// 	c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
	// 	return
	// }

	// c.JSON(http.StatusOK, gin.H{
	// 	"qr_code": visitor.QRCode,
	// })
	
	c.Redirect(http.StatusFound, "/store-loc/"+strconv.Itoa(int(qrCode.ID)))
	// c.Redirect(http.StatusFound, "/qr-codes/"+visitor.QRCode+".png")
}

// Update updates a form fill
func (ctrl *FormFillController) Update(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var formFill models.FormFill
	if err := c.ShouldBindJSON(&formFill); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	formFill.ID = uint(id)
	_, err = ctrl.repo.Update(&formFill)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, formFill)
}

// Delete deletes a form fill
func (ctrl *FormFillController) Delete(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err = ctrl.repo.Delete(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Form fill deleted"})
}
func (ctrl *FormFillController) ShowStoreLoc(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid QR Code ID"})
		return
	}

	qrCode, err := ctrl.repoQrCode.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve QR Code"})
		return
	}
	if qrCode == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "QR Code not found"})
		return
	}

	if qrCode.CodeFile == "" {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "QR Code is empty"})
		return
	}

	stores, err := ctrl.storeRepo.GetByBrochureID(qrCode.BrochureID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve store"})
		return
	}
	data := struct {
		QRCode    template.HTML
		BrochureID uint
		Stores    []models.Store
		Code	string
	}{
		QRCode:    template.HTML(qrCode.CodeFile),
		BrochureID: qrCode.BrochureID,
		Stores:    stores,
		Code: qrCode.Code,
	}
	// log.Println("Data",data)
	// Load the HTML template
	tmpl, err := template.ParseFiles("views/brochure_with_store.html")
	if err != nil {
		c.String(http.StatusInternalServerError, "Unable to load template")
		return
	}
	// Render the template with the provided data
	err = tmpl.Execute(c.Writer, data)
	if err != nil {
		c.String(http.StatusInternalServerError, "Unable to render template: "+err.Error())
	}
}
func (ctrl *FormFillController) GetOrderStore(c *gin.Context) {
	type Location struct {
		ID         int
		Name       string
		Latitude   float64
		Longitude  float64
		Address    *string
		Location   string
		Distance   float64
		OpenHours  sql.NullString // Updated to sql.NullString
		CloseHours sql.NullString // Updated to sql.NullString
	}

	// Your current location
	currentLat, err := strconv.ParseFloat(c.Query("lat"), 64)
	if err != nil {
		currentLat = 40.7128 // Example: New York City latitude
	}
	currentLong, err := strconv.ParseFloat(c.Query("long"), 64)
	if err != nil {
		currentLong = -74.0060 // Example: New York City longitude
	}

	// Brochure ID from query parameters
	brochureID, err := strconv.Atoi(c.Query("brochure_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid brochure ID"})
		return
	}

	// Query to fetch locations sorted by distance
	query := `
		SELECT s.id, s.name, s.address, s.location, s.latitude, s.longitude, od.open_hours::time, od.close_hours::time,
		(6371 * acos(cos(radians(?)) * cos(radians(s.latitude::DOUBLE PRECISION)) * cos(radians(s.longitude::DOUBLE PRECISION) - radians(?)) + sin(radians(?)) * sin(radians(s.latitude::DOUBLE PRECISION)))) AS distance
		FROM stores s
		INNER JOIN brochure_stores bs ON s.id = bs.store_id
		LEFT JOIN operational_days od ON s.id = od.store_id AND od.day = (
			CASE 
				WHEN EXTRACT(DOW FROM CURRENT_DATE) = 0 THEN 'Sunday'
				WHEN EXTRACT(DOW FROM CURRENT_DATE) = 1 THEN 'Monday'
				WHEN EXTRACT(DOW FROM CURRENT_DATE) = 2 THEN 'Tuesday'
				WHEN EXTRACT(DOW FROM CURRENT_DATE) = 3 THEN 'Wednesday'
				WHEN EXTRACT(DOW FROM CURRENT_DATE) = 4 THEN 'Thursday'
				WHEN EXTRACT(DOW FROM CURRENT_DATE) = 5 THEN 'Friday'
				WHEN EXTRACT(DOW FROM CURRENT_DATE) = 6 THEN 'Saturday'
			END
		)
		WHERE bs.brochure_id = ?
		ORDER BY distance ASC
	`

	rows, err := ctrl.storeRepo.GetDB().Raw(query, currentLat, currentLong, currentLat, brochureID).Rows()
	if err != nil {
		log.Fatal(err)
	}
	defer rows.Close()

	// Parse query results
	var locations []Location
	for rows.Next() {
		var loc Location
		var addressStr sql.NullString
		err := rows.Scan(&loc.ID, &loc.Name, &addressStr, &loc.Location, &loc.Latitude, &loc.Longitude, &loc.OpenHours, &loc.CloseHours, &loc.Distance)
		if err != nil {
			log.Fatal(err)
		}

		// Handle NULL values for address
		if addressStr.Valid {
			loc.Address = &addressStr.String
		}
		// Convert OpenHours and CloseHours
		if loc.OpenHours.Valid {
			loc.OpenHours.String = loc.OpenHours.String // Use the string value directly
		} else {
			loc.OpenHours.String = "Closed"
		}

		if loc.CloseHours.Valid {
			loc.CloseHours.String = loc.CloseHours.String
		} else {
			loc.CloseHours.String = "Closed"
		}
		log.Println("Location:", loc.CloseHours.String)
		// Add any necessary processing or formatting for open/close hours

		locations = append(locations, loc)
	}

	log.Println("Locations:", locations)

	// Return results as JSON
	c.JSON(http.StatusOK, locations)
}
// CreateFormWhatsapp creates a new form fill from form data and send whatsapp message
func (ctrl *FormFillController) CreateFormWhatsapp(c *gin.Context) {
	code := c.PostForm("code")
	caption := c.PostForm("caption")
	defaultMessage := c.PostForm("default_message")
	noWhatsapp := c.PostForm("no_whatsapp")

	if code == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "QR Code is required"})
		return
	}

	qrCode, err := ctrl.repoQrCode.GetByCode(code)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	if qrCode == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "QR Code not found"})
		return
	}

	qrCode.DefaultMessage = defaultMessage
	qrCode.Caption = caption
	err = ctrl.repoQrCode.Update(qrCode)

	// // Send whatsapp message
	// _, err = services.EbrosurAddMessage(qrCode.Phone, "6282110799347", defaultMessage, "text", "")
	// if err != nil {
	// 	c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
	// 	return
	// }
	// Redirect to whatsapp
	c.Redirect(http.StatusFound, fmt.Sprintf("https://wa.me/%s?text=%s", noWhatsapp, url.QueryEscape(defaultMessage)))
}
