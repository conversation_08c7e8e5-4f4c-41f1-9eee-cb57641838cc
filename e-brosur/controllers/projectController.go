package controllers

import (
	"net/http"
	"strconv"

	"e-brochure/models"
	"e-brochure/repositories"

	"github.com/gin-gonic/gin"
)

// ProjectController handles project-related HTTP requests
type ProjectController struct {
	repo repositories.ProjectRepository
	repoUser repositories.UserRepository
}

// NewProjectController creates a new ProjectController
func NewProjectController(repo repositories.ProjectRepository, repoUser repositories.UserRepository) *ProjectController {
	return &ProjectController{repo: repo, repoUser: repoUser}
}

// Createproject creates a new project
func (bc *ProjectController) CreateProjects(c *gin.Context) {
	var project models.Project
	currentUser, exists := c.Get("userKey")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User ID not found in context"})
	}

	// Retrieve the user from the repository
	user, err := bc.repoUser.GetByUserKey(currentUser.(string))
	if err != nil {
	}
	if user == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
	}

	project.UserID = user.ID

	// Bind JSON input to project model
	if err := c.ShouldBindJSON(&project); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input"})
		return
	}

	// Create project using the repository
	if err := bc.repo.Create(&project); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create project"})
		return
	}

	c.JSON(http.StatusCreated, project)
}

// Getprojects retrieves all projects
func (bc *ProjectController) GetAllProjects(c *gin.Context) {
	currentUser, exists := c.Get("userKey")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User ID not found in context"})
	}

	// Retrieve the user from the repository
	user, err := bc.repoUser.GetByUserKey(currentUser.(string))
	if err != nil {
	}
	if user == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
	}
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid page number"})
		return
	}
	limit, err := strconv.Atoi(c.DefaultQuery("limit", "10"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit"})
		return
	}

	name := c.Query("search")
	var projects []models.Project
	var total int64

	if name != "" {
		projects, err = bc.repo.GetByNamePaged(name, page, limit, user.ID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve projects by name"})
			return
		}

		total, err = bc.repo.CountByName(name, user.ID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count projects by name"})
			return
		}
	} else {
		projects, err = bc.repo.GetAllPaged(page, limit, user.ID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve projects"})
			return
		}

		total, err = bc.repo.Count(user.ID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count projects"})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"data":      projects,
		"page":      page,
		"limit":     limit,
		"total":     total,
		"totalPage": int((total + int64(limit) - 1) / int64(limit)),
	})
}

// Getproject retrieves a single project by ID
func (bc *ProjectController) GetProjectsByID(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID"})
		return
	}

	project, err := bc.repo.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve project"})
		return
	}
	if project == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "project not found"})
		return
	}

	c.JSON(http.StatusOK, project)
}

// Updateproject updates an existing project
func (bc *ProjectController) UpdateProjects(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID"})
		return
	}

	var project models.Project
	if err := c.ShouldBindJSON(&project); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input"})
		return
	}

	// Get last project key from db
	lastProject, err := bc.repo.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve project"})
		return
	}
	if lastProject == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "project not found"})
		return
	}

	project.ID = uint(id)                  // Set the project ID from the URL parameter
	project.ProjectKey = lastProject.ProjectKey // Set the project key from the last data on db

	// Update project using the repository
	if err := bc.repo.Update(&project); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update project"})
		return
	}

	c.JSON(http.StatusOK, project)
}

// Deleteproject deletes a project by ID
func (bc *ProjectController) DeleteProjects(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID"})
		return
	}

	if err := bc.repo.Delete(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete project"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "project deleted successfully"})
}
