package controllers

import (
	"e-brochure/models"
	"e-brochure/repositories"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// DomainController handles domain-related HTTP requests
type DomainController struct {
	repo repositories.DomainRepository
}

// NewDomainController creates a new DomainController
func NewDomainController(repo repositories.DomainRepository) *DomainController {
	return &DomainController{repo: repo}
}

// Createdomain creates a new domain
func (bc *DomainController) CreateDomains(c *gin.Context) {
	var domain models.Domain

	// Bind JSON input to domain model
	if err := c.ShouldBindJSON(&domain); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input"})
		return
	}

	// Create domain using the repository
	if err := bc.repo.Create(&domain); err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": "Failed to create domain"})
		return
	}

	c.<PERSON>(http.StatusCreated, domain)
}

// Getdomains retrieves all domains
func (bc *DomainController) GetAllDomains(c *gin.Context) {
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid page number"})
		return
	}
	limit, err := strconv.Atoi(c.DefaultQuery("limit", "10"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit"})
		return
	}

	total, err := bc.repo.Count()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count domains"})
		return
	}

	domains, err := bc.repo.GetAllPaged(page, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve domains"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":      domains,
		"page":      page,
		"limit":     limit,
		"total":     total,
		"totalPage": int((total + int64(limit) - 1) / int64(limit)),
	})
}

// Getdomain retrieves a single domain by ID
func (bc *DomainController) GetDomainsByID(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid domain ID"})
		return
	}

	domain, err := bc.repo.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve domain"})
		return
	}
	if domain == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "domain not found"})
		return
	}

	c.JSON(http.StatusOK, domain)
}

// Updatedomain updates an existing domain
func (bc *DomainController) UpdateDomains(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid domain ID"})
		return
	}

	var domain models.Domain
	if err := c.ShouldBindJSON(&domain); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input"})
		return
	}

	domain.ID = uint(id) // Set the domain ID from the URL parameter

	// Update domain using the repository
	if err := bc.repo.Update(&domain); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update domain"})
		return
	}

	c.JSON(http.StatusOK, domain)
}

// Deletedomain deletes a domain by ID
func (bc *DomainController) DeleteDomains(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid domain ID"})
		return
	}

	if err := bc.repo.Delete(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete domain"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "domain deleted successfully"})
}
