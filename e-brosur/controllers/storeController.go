package controllers

import (
	"fmt"
	"net/http"
	"strconv"

	"e-brochure/models"
	"e-brochure/repositories"

	"github.com/gin-gonic/gin"
)

// StoreController handles store-related HTTP requests
type StoreController struct {
	repo repositories.StoreRepository
	repoUser repositories.UserRepository
}

// NewStoreController creates a new StoreController
func NewStoreController(repo repositories.StoreRepository, repoUser repositories.UserRepository) *StoreController {
	return &StoreController{
		repo: repo,
		repoUser: repoUser,
	}
}

// CreateStore creates a new store
func (sc *StoreController) CreateStore(c *gin.Context) {
	var store models.Store

	// Bind JSON input to store model
	if err := c.Should<PERSON>ind<PERSON>(&store); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input", "details": err.Error()})
		return
	}

	// Get user ID from the context
	currentUser, exists := c.Get("userKey")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User ID not found in context"})
		return
	}

	// Retrieve the user from the repository
	user, err := sc.repoUser.GetByUserKey(currentUser.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve user project key"})
		return
	}
	if user == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	store.ProjectKey = user.ProjectKey

	// Create store using the repository
	if err := sc.repo.Create(&store); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create store", "details": err.Error()})
		return
	}
	store.OperationalDays = nil

	// Create operational days and associate them with the store
	for _, operationalDayInput := range store.OperationalDays {
		operationalDayInput.StoreID = store.ID
		// Check if an operational day with the same details already exists
		existingDay, _ := sc.repo.FindOperationalDay(&operationalDayInput)
		if existingDay != nil {
			continue // Skip duplicate entries
		}

		// Create the operational day
		operationalDay, err := sc.repo.CreateOperationalDay(&operationalDayInput)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create operational day", "details": err.Error()})
			return
		}
		store.OperationalDays = append(store.OperationalDays, *operationalDay)
	}
	// Retrieve the newly created store using the repository
	storeReturn, err := sc.repo.GetByID(store.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve store", "details": err.Error()})
		return
	}
	c.JSON(http.StatusCreated, storeReturn)
}
// GetStores retrieves all stores
func (sc *StoreController) GetStores(c *gin.Context) {
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid page number"})
		return
	}
	limit, err := strconv.Atoi(c.DefaultQuery("limit", "10"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit"})
		return
	}

	name := c.Query("search")
	
	user, err := sc.getUser(c)
	if err != nil {
		return
	}
	if name != "" {
		stores, err := sc.repo.GetByNamePaged(name, page, limit, user.ProjectKey)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve stores by name"})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data": stores,
			"page": page,
			"limit": limit,
		})
		return
	}

	total, err := sc.repo.Count()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count stores"})
		return
	}

	stores, err := sc.repo.GetAllPaged(page, limit, user.ProjectKey)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve stores"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":      stores,
		"page":      page,
		"limit":     limit,
		"total":     total,
		"totalPage": int((total + int64(limit) - 1) / int64(limit)),
	})
}

// GetStore retrieves a single store by ID
func (sc *StoreController) GetStore(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid store ID"})
		return
	}

	store, err := sc.repo.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve store"})
		return
	}
	if store == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Store not found"})
		return
	}

	c.JSON(http.StatusOK, store)
}

// UpdateStore updates an existing store
func (sc *StoreController) UpdateStore(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid store ID"})
		return
	}

	var store models.Store
	if err := c.ShouldBindJSON(&store); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input"})
		return
	}

	user, err := sc.getUser(c)
	if err != nil {
		return
	}

	store.ID = uint(id)                // Set the store ID from the URL parameter
	store.ProjectKey = user.ProjectKey // Set the project key from the user token

	// Update store using the repository
	if err := sc.repo.Update(&store); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update store"})
		return
	}

	c.JSON(http.StatusOK, store)
}

// DeleteStore deletes a store by ID
func (sc *StoreController) DeleteStore(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid store ID"})
		return
	}

	if err := sc.repo.Delete(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete store"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Store deleted successfully"})
}
func (sc *StoreController) getUser(c *gin.Context) (*models.User, error) {
	// Get user ID from the context
	currentUser, exists := c.Get("userKey")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User ID not found in context"})
		return nil, fmt.Errorf("User ID not found in context")
	}

	// Retrieve the user from the repository
	user, err := sc.repoUser.GetByUserKey(currentUser.(string))
	if err != nil {
		return nil, fmt.Errorf("Failed to retrieve user project key")
	}
	if user == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return nil, fmt.Errorf("User not found")
	}
	if user.ProjectKey == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User project key is required"})
		return nil, fmt.Errorf("User project key is required")
	}

	return user, nil
}