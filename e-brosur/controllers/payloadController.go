package controllers

import (
	"e-brochure/models"
	"e-brochure/repositories"
	"encoding/json"
	"net/http"

	"github.com/gin-gonic/gin"
)

// PayloadController handles payload-related HTTP requests
type PayloadController struct {
	repo repositories.PayloadRepository
}

// NewPayloadController creates a new PayloadController
func NewPayloadController(repo repositories.PayloadRepository) *PayloadController {
	return &PayloadController{repo: repo}
}

// CreatePayload creates a new payload
func (pc *PayloadController) CreatePayload(c *gin.Context) {
		// Parse the form data
	if err := c.Request.ParseForm(); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse form"})
		return
	}
  	body := c.Request.FormValue("body")
    jsonBody, err := json.Marshal(map[string]string{"body": body})
    if err != nil {
        c.<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid JSON format"})
        return
    }

    // Create the payload object
    payload := models.Payload{
        Body: string(jsonBody),
    }

    // Store the payload in the repository
    if err := pc.repo.Create(&payload); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create payload"})
        return
    }

    // Return success response
    c.JSON(http.StatusOK, gin.H{
        "message": "Payload created successfully",
        "payload": payload,
    })
}


