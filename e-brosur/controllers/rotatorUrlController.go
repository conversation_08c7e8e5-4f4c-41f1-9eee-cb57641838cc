package controllers

import (
	"fmt"
	"net/http"
	"strconv"

	"e-brochure/models"
	"e-brochure/repositories"

	"github.com/gin-gonic/gin"
)

// RotatorURLController handles RotatorURL-related HTTP requests
type RotatorURLController struct {
	repo repositories.RotatorURLRepository
	repoUser repositories.UserRepository
}

// NewRotatorURLController creates a new RotatorURLController
func NewRotatorURLController(repo repositories.RotatorURLRepository, repoUser repositories.UserRepository) *RotatorURLController {
	return &RotatorURLController{repo: repo, repoUser: repoUser}
}

// CreateRotatorURL creates a new RotatorURL
func (rc *RotatorURLController) CreateRotatorURL(c *gin.Context) {
	var rotator models.RotatorURL

	// Bind JSON input to RotatorURL model
	if err := c.ShouldBindJSON(&rotator); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input"})
		return
	}

	// Create RotatorURL using the repository
	if err := rc.repo.Create(&rotator); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create RotatorURL"})
		return
	}

	c.JSON(http.StatusCreated, rotator)
}

// GetRotatorURLs retrieves all RotatorURLs
func (rc *RotatorURLController) GetRotatorURLs(c *gin.Context) {
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid page number"})
		return
	}
	limit, err := strconv.Atoi(c.DefaultQuery("limit", "10"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit"})
		return
	}

	user, err := rc.getUser(c)
	if err != nil {
		return
	}
	total, err := rc.repo.Count(user.ProjectKey)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count RotatorURLs"})
		return
	}

	rotators, err := rc.repo.GetAllPaged(page, limit, user.ProjectKey)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve RotatorURLs"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":      rotators,
		"page":      page,
		"limit":     limit,
		"total":     total,
		"totalPage": int((total + int64(limit) - 1) / int64(limit)),
	})
}

// GetRotatorURL retrieves a single RotatorURL by ID
func (rc *RotatorURLController) GetRotatorURL(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid RotatorURL ID"})
		return
	}

	rotator, err := rc.repo.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve RotatorURL"})
		return
	}
	if rotator == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "RotatorURL not found"})
		return
	}

	c.JSON(http.StatusOK, rotator)
}

// UpdateRotatorURL updates an existing RotatorURL
func (rc *RotatorURLController) UpdateRotatorURL(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid RotatorURL ID"})
		return
	}

	var rotator models.RotatorURL
	if err := c.ShouldBindJSON(&rotator); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input"})
		return
	}

	rotator.ID = uint(id) // Set the RotatorURL ID from the URL parameter

	// Update RotatorURL using the repository
	if err := rc.repo.Update(&rotator); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update RotatorURL"})
		return
	}

	c.JSON(http.StatusOK, rotator)
}

// DeleteRotatorURL deletes a RotatorURL by ID
func (rc *RotatorURLController) DeleteRotatorURL(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid RotatorURL ID"})
		return
	}

	if err := rc.repo.Delete(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete RotatorURL"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "RotatorURL deleted successfully"})
}
func (rc *RotatorURLController) getUser(c *gin.Context) (*models.User, error) {
	// Get user ID from the context
	currentUser, exists := c.Get("userKey")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User ID not found in context"})
		return nil, fmt.Errorf("User ID not found in context")
	}

	// Retrieve the user from the repository
	user, err := rc.repoUser.GetByUserKey(currentUser.(string))
	if err != nil {
		return nil, fmt.Errorf("Failed to retrieve user project key")
	}
	if user == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return nil, fmt.Errorf("User not found")
	}
	if user.ProjectKey == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User project key is required"})
		return nil, fmt.Errorf("User project key is required")
	}

	return user, nil
}