package controllers

import (
	"e-brochure/models"
	"e-brochure/repositories"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type AffiliateController struct {
	repo repositories.AffiliateRepository
}

func NewAffiliateController(repo repositories.AffiliateRepository) *AffiliateController {
	return &AffiliateController{repo: repo}
}

// CreateAffiliate handles creating a new affiliate
func (ctrl *AffiliateController) CreateAffiliate(c *gin.Context) {
	var input struct {
		Code       string  `json:"code" binding:"required"`
		Commission float64 `json:"commission" binding:"required,gte=0"`
	}

	// Bind JSON request body to the input struct
	if err := c.ShouldBindJSON(&input); err != nil {
		// Validation error if the JSON is not valid
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Validation error",
			"code":    http.StatusBadRequest,
			"errors":  []string{err.Error()},
			"result":  nil,
		})
		return
	}

	// Validate Commission value
	if input.Commission <= 0 {
		c.JSO<PERSON>(http.StatusBadRequest, gin.H{"error": "Commission must be greater than zero"})
		return
	}

	// Check if an affiliate with the same code already exists
	existingAffiliate, err := ctrl.repo.GetAffiliateByCode(input.Code)
	if err != nil && err.Error() != "record not found" {
		// If any unexpected error occurs while checking for the code
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Internal server error",
			"code":    http.StatusInternalServerError,
			"result":  nil,
		})
		return
	}

	// If affiliate already exists, return a response with a conflict error
	if existingAffiliate != nil {
		c.JSON(http.StatusConflict, gin.H{
			"message": "Affiliate code already exists",
			"code":    0,
			"result":  []interface{}{}, // Empty array as the result
		})
		return
	}

	// Generate Affiliate URL based on code (use a dummy URL format)
	affiliateURL := "http://localhost:8080/" + input.Code

	// Create Affiliate object to save in the database
	affiliate := models.Affiliate{
		Code:         input.Code,
		Commission:   input.Commission,
		AffiliateURL: affiliateURL,
	}

	// Save the affiliate in the repository
	if err := ctrl.repo.CreateAffiliate(&affiliate); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create affiliate"})
		return
	}

	// Return success response
	c.JSON(http.StatusOK, gin.H{
		"message": "Affiliate created successfully",
		"code":    1,
		"result":  affiliate,
	})
}

func (ctrl *AffiliateController) GetAffiliates(c *gin.Context) {
	affiliates, err := ctrl.repo.GetAffiliates()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Return response sukses dengan message, code, dan result
	c.JSON(http.StatusOK, gin.H{
		"message": "data berhasil didapatkan",
		"code":    1,
		"result":  affiliates,
	})
}

func (ctrl *AffiliateController) GetAffiliate(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid ID format",
			"code":    http.StatusBadRequest,
			"result":  nil,
		})
		return
	}

	// Get affiliate by ID
	affiliate, err := ctrl.repo.GetAffiliate(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Internal server error",
			"code":    http.StatusInternalServerError,
			"result":  nil,
		})
		return
	}

	if affiliate == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Affiliate not found",
			"code":    http.StatusNotFound,
			"result":  nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Affiliate retrieved successfully",
		"code":    http.StatusOK,
		"result":  affiliate,
	})
}
func (ctrl *AffiliateController) UpdateAffiliate(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid ID format",
			"code":    http.StatusBadRequest,
			"result":  nil,
		})
		return
	}

	// Retrieve the existing affiliate
	affiliate, err := ctrl.repo.GetAffiliate(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Internal server error",
			"code":    http.StatusInternalServerError,
			"result":  nil,
		})
		return
	}

	if affiliate == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Affiliate not found",
			"code":    http.StatusNotFound,
			"result":  nil,
		})
		return
	}

	// Bind JSON request body to the affiliate struct
	var input struct {
		Code       string  `json:"code" binding:"required"`
		Commission float64 `json:"commission" binding:"required,gte=0"`
	}
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Validation error",
			"code":    http.StatusBadRequest,
			"errors":  []string{err.Error()},
			"result":  nil,
		})
		return
	}

	// Update the affiliate fields
	affiliate.Code = input.Code
	affiliate.Commission = input.Commission

	// Save the updated affiliate to the database
	if err := ctrl.repo.UpdateAffiliate(affiliate); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to update affiliate",
			"code":    http.StatusInternalServerError,
			"result":  nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Affiliate updated successfully",
		"code":    http.StatusOK,
		"result":  affiliate,
	})
}
func (ctrl *AffiliateController) DeleteAffiliate(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid ID format",
			"code":    http.StatusBadRequest,
			"result":  nil,
		})
		return
	}

	// Check if the affiliate exists
	affiliate, err := ctrl.repo.GetAffiliate(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Internal server error",
			"code":    http.StatusInternalServerError,
			"result":  nil,
		})
		return
	}

	if affiliate == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Affiliate not found",
			"code":    http.StatusNotFound,
			"result":  nil,
		})
		return
	}

	// Delete the affiliate
	if err := ctrl.repo.DeleteAffiliate(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to delete affiliate",
			"code":    http.StatusInternalServerError,
			"result":  nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Affiliate deleted successfully",
		"code":    http.StatusOK,
		"result":  nil,
	})
}
