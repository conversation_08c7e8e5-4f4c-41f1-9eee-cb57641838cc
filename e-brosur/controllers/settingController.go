package controllers

import (
	"e-brochure/models"
	"e-brochure/repositories"
	"e-brochure/services"
	"errors"
	"fmt"
	"image"
	"image/draw"
	"image/jpeg"
	"net/http"
	"os"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/nfnt/resize"
	"gorm.io/gorm"
)

type SettingsController struct {
    settingsRepo repositories.SettingRepository
    userRepo repositories.UserRepository
    storeRepo repositories.StoreRepository
    repoQrCode repositories.QrCodeRepository
}

func NewSettingsController(r repositories.SettingRepository, u repositories.UserRepository, storeRepo repositories.StoreRepository, repoQrCode repositories.QrCodeRepository) *SettingsController {
    return &SettingsController{settingsRepo: r, userRepo: u, storeRepo: storeRepo, repoQrCode: repoQrCode}
}

func (ctrl *SettingsController) GetAllSettings(c *gin.Context) {
    page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid page number"})
        return
    }
    limit, err := strconv.Atoi(c.DefaultQuery("limit", "10"))
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit"})
        return
    }

    total, err := ctrl.settingsRepo.Count()
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count settings"})
        return
    }

    settings, err := ctrl.settingsRepo.GetAllPaged(page, limit)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve settings"})
        return
    }

    c.JSON(http.StatusOK, gin.H{
        "data":      settings,
        "page":      page,
        "limit":     limit,
        "total":     total,
        "totalPage": int((total + int64(limit) - 1) / int64(limit)),
    })
}

func (ctrl *SettingsController) GetSettingsByID(c *gin.Context) {
    id, err := strconv.Atoi(c.Param("id"))
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
        return
    }

    settings, err := ctrl.settingsRepo.FindByID(uint(id))
    if err != nil {
        c.JSON(http.StatusNotFound, gin.H{"error": "Settings not found"})
        return
    }

    c.JSON(http.StatusOK, settings)
}

func (ctrl *SettingsController) CreateSettings(c *gin.Context) {
    var settings models.Setting
    if err := c.ShouldBindJSON(&settings); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    if err := ctrl.settingsRepo.Create(settings); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusCreated, settings)
}

func (ctrl *SettingsController) UpdateSettings(c *gin.Context) {
    id, err := strconv.Atoi(c.Param("id"))
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
        return
    }

    var settings models.Setting
    if err := c.ShouldBindJSON(&settings); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    settings.ID = uint(id) // Set the ID from the path
    if err := ctrl.settingsRepo.Update(settings); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusOK, settings)
}

func (ctrl *SettingsController) DeleteSettings(c *gin.Context) {
    id, err := strconv.Atoi(c.Param("id"))
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
        return
    }

    if err := ctrl.settingsRepo.Delete(uint(id)); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.Status(http.StatusNoContent)
}

func (ctrl *SettingsController) UpdateProjectKey(c *gin.Context) {
    currentUser, exists := c.Get("userKey")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User ID not found in context"})
		return
	}    
    user, err := ctrl.userRepo.GetByUserKey(currentUser.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve user project key"})
		return
	}
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user project key"})
		return
	}
	if user == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}
    
    var update struct {
        ProjectKey string `json:"project_key"`
    }
    if err := c.ShouldBindJSON(&update); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    user.ProjectKey = update.ProjectKey
    if err := ctrl.userRepo.Update(user); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusOK, user)
}

func (ctrl *SettingsController) GetUserData(c *gin.Context) {
    currentUser, exists := c.Get("userKey")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User ID not found in context"})
		return
	}
    
    user, err := ctrl.userRepo.GetByUserKey(currentUser.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user data"})
		return
	}
	if user == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

    c.JSON(http.StatusOK, user)
}

func (ctrl *SettingsController) UpdateStoreUser(c *gin.Context) {
    currentUser, exists := c.Get("userKey")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User ID not found in context"})
		return
	}
    
    var update struct {
        StoreID uint `json:"store_id"`
    }
    if err := c.ShouldBindJSON(&update); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    user, err := ctrl.userRepo.GetByUserKey(currentUser.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user project key"})
		return
	}
	if user == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}
    
    user.StoreID = &update.StoreID
    if err := ctrl.userRepo.Update(user); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusOK, user)
}
func (ctrl *SettingsController) AddWaConfig(c *gin.Context) {
    currentUser, exists := c.Get("userKey")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User ID not found in context"})
		return
	}
    
    user, err := ctrl.userRepo.GetByUserKey(currentUser.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user project key"})
		return
	}
	if user == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

    var waConfig models.User
    if err := c.ShouldBindJSON(&waConfig); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    response, err := services.EbrosurCsAdd(user.Name, user.Phone, user.Email, *waConfig.CsName, *waConfig.CsPhone, *waConfig.CsWebhook)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    // if responseCode := response["code"].(float64); responseCode == 200 {
    user.CsName = waConfig.CsName
    user.CsPhone = waConfig.CsPhone
    user.CsWebhook = waConfig.CsWebhook
    if err := ctrl.userRepo.Update(user); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    // }
    c.JSON(http.StatusOK, gin.H{"message": response})
}
func (ctrl *SettingsController) GetWhatsappNumber(c *gin.Context) {
    currentUser, exists := c.Get("userKey")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User ID not found in context"})
		return
	}
    
    user, err := ctrl.userRepo.GetByUserKey(currentUser.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user project key"})
		return
	}
	if user == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}
    response := []map[string]interface{}{
        {
            "whatsapp_number": user.Phone,
        },
    }
    whatsappUsers, err := ctrl.userRepo.GetAllByParentID(user.ID)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get whatsapp user"})
        return
    }
    for _, whatsappUser := range whatsappUsers {
        response = append(response, map[string]interface{}{
            "whatsapp_number": whatsappUser.Phone,
        })
    }
    c.JSON(http.StatusOK, response)
}

func (ctrl *SettingsController) WhatsappPayload(c *gin.Context) {
    message := c.PostForm("message")
    phone := c.PostForm("phone")
    nope_cs := c.PostForm("nope_cs")

    if message == "" || phone == "" {
        c.JSON(http.StatusBadRequest, gin.H{"error": "message and phone are required"})
        return
    }

    qrCode, err := ctrl.repoQrCode.GetByDefaultMessage(message)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get qr code"})
        return
    }
    if qrCode == nil {
        c.JSON(http.StatusNotFound, gin.H{"error": "QR Code not found"})
        return
    }
    // log.Println("code ",qrCode)
    file1, err := os.Open(qrCode.CodeFile)
    if err != nil {
		fmt.Println("Error opening image1 1:", err)
        return
    }
    defer file1.Close()

	img1, _, err := image.Decode(file1)
	if err != nil {
		fmt.Println("Error decoding image1:", err)
		return
	}
	fmt.Println("Image1 decoded successfully!")

    file2, err := os.Open("qr_codes/bg.jpg")
	if err != nil {
		fmt.Println("Error opening image2:", err)
		return
	}
	defer file2.Close()

	img2, _, err := image.Decode(file2)
	if err != nil {
		fmt.Println("Error decoding image2:", err)
		return
	}
	fmt.Println("Image2 decoded successfully!")

    
	resultImg := concatenateImages(img1, img2)

    
	outputFile, err := os.Create("qr_codes/output"+qrCode.Code+".jpg")
	if err != nil {
		fmt.Println("Error creating output file:", err)
		return
	}
	defer outputFile.Close()

    
	err = jpeg.Encode(outputFile, resultImg, nil)
	if err != nil {
		fmt.Println("Error encoding output image:", err)
		return
	}

    qrCode.MergeFile = "qr_codes/output"+qrCode.Code+".jpg"
    if err := ctrl.repoQrCode.Update(qrCode); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    _, err = services.EbrosurAddMessage(nope_cs, phone, qrCode.Caption, "jpg", "https://api-ebrosur.gass.co.id/qr_codes/output"+qrCode.Code+".jpg")
    // _, err = services.EbrosurAddMessage(nope_cs, phone, qrCode.Caption, "jpg", "http://"+c.Request.Host+"/" + qrCode.CodeFile)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusOK, gin.H{"message": "Success send whatsapp message"})
}

func concatenateImages(img1, img2 image.Image) image.Image {
	
	margin_top := 50

	// Get dimensions of both images
	width2 := img2.Bounds().Dx()
	height2 := img2.Bounds().Dy()
	


	// Create a new blank image with the computed dimensions
	result := image.NewRGBA(image.Rect(0, 0, width2, height2))

	// Draw image2 at the top of the resulting image (left-aligned)
	draw.Draw(result, img2.Bounds(), img2, image.Point{0, 0}, draw.Over)
	
	width1 := int(float64(img1.Bounds().Dx()) * 0.8)
	height1 := int(float64(img1.Bounds().Dy()) * 0.8)

	img1 = resize.Resize(uint(width1), uint(height1), img1, resize.Lanczos3)

	// Draw image1 below image2, centered horizontally
	offset1 := image.Point{
		X: (width2 - width1) / 2, // Center horizontally
		Y: margin_top, // Center vertically
	}
	draw.Draw(result, img1.Bounds().Add(offset1), img1, image.Point{0, 0}, draw.Over)

	return result
}

func (ctrl *SettingsController) GetSettingsByProjectKey(c *gin.Context) {
    user, err := ctrl.getUser(c)
	if err != nil {
		return
	}
	projectKey := user.ProjectKey

	settings, err := ctrl.settingsRepo.GetByProjectKey(projectKey)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	settingsMap := map[string]interface{}{
		"mql":      settings.MQL,
		"formatId": settings.FormatID,
	}

	c.JSON(http.StatusOK, settingsMap)
}
func (ctrl *SettingsController) UpdateSettingsByProjectKey(c *gin.Context) {
    user, err := ctrl.getUser(c)
	if err != nil {
		return
	}
	projectKey := user.ProjectKey

	var update struct {
		MQL      *int    `json:"mql"`
		FormatID *string `json:"format_id"`
	}
	if err := c.ShouldBindJSON(&update); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var settings *models.Setting
	settings, err = ctrl.settingsRepo.GetByProjectKey(projectKey)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			settings = &models.Setting{
				ProjectKey: projectKey,
				MQL:        update.MQL,
				FormatID:   update.FormatID,
			}

            if err := ctrl.settingsRepo.Create(*settings); err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
            }
	        c.JSON(http.StatusOK, settings)
			return
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
	}
	if settings == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Settings not found"})
		return
	}

	settings.MQL = update.MQL
	settings.FormatID = update.FormatID

	if err := ctrl.settingsRepo.Update(*settings); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, settings)
}
func (ctrl *SettingsController) getUser(c *gin.Context) (*models.User, error) {
	// Get user ID from the context
	currentUser, exists := c.Get("userKey")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User ID not found in context"})
		return nil, fmt.Errorf("User ID not found in context")
	}

	// Retrieve the user from the repository
	user, err := ctrl.userRepo.GetByUserKey(currentUser.(string))
	if err != nil {
		return nil, fmt.Errorf("Failed to retrieve user project key")
	}
	if user == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return nil, fmt.Errorf("User not found")
	}
	if user.ProjectKey == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User project key is required"})
		return nil, fmt.Errorf("User project key is required")
	}

	return user, nil
}