package controllers

import (
	"e-brochure/models"
	setup "e-brochure/models"
	"e-brochure/repositories"
	helper "e-brochure/utils"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
)

func TokenSso(c *gin.Context) {
	client := &http.Client{
		Timeout: time.Second * 10,
	}
	var result map[string]interface{}
	data := url.Values{}
	data.Set("grant_type", "authorization_code")
	data.Set("client_secret", "17fbcda30bc800f8e09081a2abcf0afecd52a4ff6f9e91832e7fc5c1084cd88a")
	data.Add("client_id", "ebrosur2637823467899103xfs")
	data.Add("code", c.Query("code"))

	encodedData := data.Encode()
	fmt.Println(encodedData)
	req, err := http.NewRequest(http.MethodPost, "https://n.gass.co.id/token", strings.NewReader(encodedData))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	response, err := client.Do(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	defer response.Body.Close()
	body, _ := io.ReadAll(response.Body)

	log.Println(body)
	if err := json.Unmarshal(body, &result); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to unmarshal response: %v", err)})
		return
	}

	// Check if keys exist and are not nil
	name, ok := result["name"].(string)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Name is missing or not a string"})
		return
	}

	userKey, ok := result["user_key"].(string)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "User key is missing or not a string"})
		return
	}

	phone, ok := result["phone"].(string)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Phone is missing or not a string"})
		return
	}

	email, ok := result["email"].(string)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Email is missing or not a string"})
		return
	}

	db := setup.InitializeDB()
	token, err := helper.GenerateToken(name,userKey)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Token generation failed: %v", err)})
		return
	}

	result["token"] = token
	userRepo := repositories.NewUserRepository(db)

	existingUser, err := userRepo.GetByUserKey(userKey)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check existing user"})
		return
	}
	if existingUser == nil {
		newUser := &models.User{
			Token:   token,
			UserKey: userKey,
			Name:    name,
			Phone:   phone,
			Email:   email,
		}
		if err := userRepo.Create(newUser); err != nil {
			log.Fatalf("Failed to create user: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
			return
		}	
		result["user"] = newUser
	} else {
		newUser := existingUser
		result["user"] = newUser
		
		projects, err := userRepo.GetProjectsByUserID(existingUser.ID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get projects"})
			return
		}
		result["projects"] = projects
	}
	c.JSON(http.StatusOK, gin.H{"message": "Request created successfully", "data": result})
}

func LoginUsingPhoneAndPassword(c *gin.Context) {
	var userInput struct {
		Phone    string `json:"phone"`
		Password string `json:"password"`
	}

	if err := c.ShouldBindJSON(&userInput); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Phone and password are required"})
		return
	}

	phone := userInput.Phone
	password := userInput.Password

	if phone == "" || password == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Phone and password are required"})
		return
	}

	db := setup.InitializeDB()
	userRepo := repositories.NewUserRepository(db)
	user, err := userRepo.GetByPhone(phone)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user"})
		return
	}
	if user == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}
	if err := helper.CheckPasswordHash(password, string(user.Password)); err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Wrong password"})
		return
	}

	token, err := helper.GenerateToken(user.Name, user.UserKey)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Token generation failed: %v", err)})
		return
	}

	result := map[string]interface{}{
		"token": token,
		"user":  user,
	}

	c.JSON(http.StatusOK, gin.H{"message": "Login successfully", "data": result})
}

func RegisterUsingPhoneAndPassword(c *gin.Context) {
	var userInput struct {
		Phone    string `json:"phone"`
		Password string `json:"password"`
	}

	if err := c.ShouldBindJSON(&userInput); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Phone and password are required"})
		return
	}

	phone := userInput.Phone
	password := userInput.Password

	if phone == "" || password == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Phone and password are required"})
		return
	}

	db := setup.InitializeDB()
	userRepo := repositories.NewUserRepository(db)
	existingUser, err := userRepo.GetByPhone(phone)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check existing user"})
		return
	}
	if existingUser != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User already exists"})
		return
	}

	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to encrypt password"})
		return
	}

	newUser := &models.User{
		Phone:    phone,
		Password: hashedPassword,
	}
	if err := userRepo.Create(newUser); err != nil {
		log.Fatalf("Failed to create user: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
		return
	}

	result := map[string]interface{}{
		"user": newUser,
	}

	c.JSON(http.StatusOK, gin.H{"message": "Register successfully", "data": result})
}
