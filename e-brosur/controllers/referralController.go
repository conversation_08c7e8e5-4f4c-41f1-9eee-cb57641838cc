package controllers

import (
	"e-brochure/models"
	"e-brochure/repositories"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// ReferralController handles referral-related HTTP requests
type ReferralController struct {
	repo *repositories.ReferralRepository
}

// NewReferralController creates a new ReferralController
func NewReferralController(repo *repositories.ReferralRepository) *ReferralController {
	return &ReferralController{repo: repo}
}

// CreateReferral handles creating a new referral
func (ctrl *ReferralController) CreateReferral(c *gin.Context) {
	var input struct {
		Name             string  `json:"name" binding:"required"`
		Phone            string  `json:"phone" binding:"required"`
		TotalTransaction float64 `json:"total_transaction" binding:"required,gte=0"`
		AffiliateID      uint    `json:"affiliate_id" binding:"required"`
	}

	// Bind the incoming JSON to the input struct
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Validation error",
			"code":    http.StatusBadRequest,
			"errors":  []string{err.Error()},
			"result":  nil,
		})
		return
	}

	// Fetch the affiliate data to calculate the commission
	affiliate, err := ctrl.repo.GetAffiliateByID(input.AffiliateID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve affiliate"})
		return
	}

	if affiliate == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Affiliate not found"})
		return
	}

	// Calculate the total commission based on the affiliate's commission percentage
	totalCommission := (input.TotalTransaction * affiliate.Commission) / 100

	// Create a new Referral object with calculated total commission
	referral := models.Referral{
		Name:             input.Name,
		Phone:            input.Phone,
		TotalTransaction: input.TotalTransaction,
		TotalCommission:  totalCommission,
		AffiliateID:      input.AffiliateID,
	}

	// Save the referral to the database
	if err := ctrl.repo.CreateReferral(&referral); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create referral"})
		return
	}

	// Preload the associated Affiliate data using the new function
	referralWithAffiliate, err := ctrl.repo.GetReferralWithAffiliate(referral.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to load affiliate data"})
		return
	}

	// Return success response with the loaded affiliate data
	c.JSON(http.StatusOK, gin.H{
		"message": "Referral created successfully",
		"code":    1,
		"result":  referralWithAffiliate,
	})
}

// GetReferrals handles fetching all referrals
func (ctrl *ReferralController) GetReferrals(c *gin.Context) {
	referrals, err := ctrl.repo.GetReferrals()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Referrals fetched successfully",
		"code":    1,
		"result":  referrals,
	})
}

// GetReferralByID handles fetching a referral by ID, including its associated Affiliate
func (ctrl *ReferralController) GetReferralByID(c *gin.Context) {
	// Get the referral ID from the URL parameters
	id := c.Param("id")

	// Convert the ID to uint
	referralID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid referral ID"})
		return
	}

	// Fetch the referral using the repository
	referral, err := ctrl.repo.GetReferralWithAffiliate(uint(referralID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch referral"})
		return
	}

	if referral == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Referral not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Referral fetched successfully",
		"code":    1,
		"result":  referral,
	})
}

// DeleteReferral handles deleting a referral by ID
func (ctrl *ReferralController) DeleteReferral(c *gin.Context) {
	// Get the referral ID from the URL parameters
	id := c.Param("id")

	// Convert the ID to uint
	referralID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid referral ID"})
		return
	}

	// Delete the referral using the repository
	if err := ctrl.repo.DeleteReferral(uint(referralID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete referral"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Referral deleted successfully",
		"code":    1,
		"result":  nil,
	})
}
