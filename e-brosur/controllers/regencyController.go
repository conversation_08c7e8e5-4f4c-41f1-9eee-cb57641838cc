package controllers

import (
	"net/http"
	"strconv"

	"e-brochure/models"
	"e-brochure/repositories"

	"github.com/gin-gonic/gin"
)

// RegencyController handles regency-related HTTP requests
type RegencyController struct {
	repo repositories.RegencyRepository
}

// NewRegencyController creates a new RegencyController
func NewRegencyController(r repositories.RegencyRepository) *RegencyController {
	return &RegencyController{repo: r}
}

// GetAllRegencies returns all regencies in the database
func (ctrl *RegencyController) GetAllRegencies(c *gin.Context) {
	name := c.Query("search")
	if name != "" {
		regencies, err := ctrl.repo.GetByName(name)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSO<PERSON>(http.StatusOK, regencies)
		return
	}

	regencies, err := ctrl.repo.GetAll()
	if err != nil {
		c.J<PERSON>N(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, regencies)
}

// GetRegencyByID returns a regency by ID
func (ctrl *RegencyController) GetRegencyByID(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	regency, err := ctrl.repo.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, regency)
}

// CreateRegency creates a new regency
func (ctrl *RegencyController) CreateRegency(c *gin.Context) {
	var regency models.Regency
	if err := c.ShouldBindJSON(&regency); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := ctrl.repo.Create(&regency); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, regency)
}

// UpdateRegency updates a regency
func (ctrl *RegencyController) UpdateRegency(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	var regency models.Regency
	if err := c.ShouldBindJSON(&regency); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	regency.ID = uint(id)
	if err := ctrl.repo.Update(&regency); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, regency)
}

// DeleteRegency deletes a regency
func (ctrl *RegencyController) DeleteRegency(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	if err := ctrl.repo.Delete(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Regency deleted"})
}
