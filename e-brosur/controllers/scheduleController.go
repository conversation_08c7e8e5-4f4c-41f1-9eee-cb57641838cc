package controllers

import (
	"net/http"
	"strconv"

	"e-brochure/models"
	"e-brochure/repositories"

	"github.com/gin-gonic/gin"
)

// ScheduleController handles schedule-related HTTP requests
type ScheduleController struct {
	repo repositories.ScheduleRepository
}

// NewScheduleController creates a new schedule controller
func NewScheduleController(repo repositories.ScheduleRepository) *ScheduleController {
	return &ScheduleController{repo: repo}
}

// GetAllSchedules returns all schedules
func (sc *ScheduleController) GetAllSchedules(c *gin.Context) {
	schedules, err := sc.repo.GetAll()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, schedules)
}

// GetScheduleByID returns a schedule by ID
func (sc *ScheduleController) GetScheduleByID(c *gin.Context) {
	id, err := strconv.Atoi(c.<PERSON>m("id"))
	if err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid schedule ID"})
		return
	}
	schedule, err := sc.repo.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	if schedule == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Schedule not found"})
		return
	}
	c.JSON(http.StatusOK, schedule)
}

// CreateSchedule creates a new schedule
func (sc *ScheduleController) CreateSchedule(c *gin.Context) {
	var schedule models.Schedule
	if err := c.ShouldBindJSON(&schedule); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	if err := sc.repo.Create(&schedule); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusCreated, schedule)
}

// UpdateSchedule updates an existing schedule
func (sc *ScheduleController) UpdateSchedule(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid schedule ID"})
		return
	}
	schedule, err := sc.repo.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	if schedule == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Schedule not found"})
		return
	}
	var update models.Schedule
	if err := c.ShouldBindJSON(&update); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	schedule.Task = update.Task
	schedule.Date = update.Date
	schedule.Platform = update.Platform
	schedule.RateCard = update.RateCard
	if err := sc.repo.Update(schedule); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, schedule)
}

// DeleteSchedule deletes a schedule
func (sc *ScheduleController) DeleteSchedule(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid schedule ID"})
		return
	}
	schedule, err := sc.repo.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	if schedule == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Schedule not found"})
		return
	}
	if err := sc.repo.Delete(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Schedule deleted successfully"})
}
