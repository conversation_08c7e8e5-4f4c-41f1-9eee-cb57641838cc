package controllers

import (
	"fmt"
	"net/http"
	"strconv"

	"e-brochure/models"
	"e-brochure/repositories"

	"github.com/gin-gonic/gin"
)

// ConnectorController handles connector-related HTTP requests
type ConnectorController struct {
	repo repositories.ConnectorRepository
	repoUser repositories.UserRepository
}

// NewConnectorController creates a new ConnectorController
func NewConnectorController(repo repositories.ConnectorRepository, repoUser repositories.UserRepository) *ConnectorController {
	return &ConnectorController{
		repo: repo,
		repoUser:repoUser,
	}
}

// CreateConnector creates a new connector
func (ctrl *ConnectorController) CreateConnector(c *gin.Context) {
	var connector models.Connector
	user, err := ctrl.getUser(c)
	if err != nil {
		return
	}

	connector.ProjectKey = user.ProjectKey
	if err := c.ShouldBind<PERSON>SO<PERSON>(&connector); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := ctrl.repo.Create(&connector); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, connector)
}

// GetConnectors retrieves all connectors
func (ctrl *ConnectorController) GetConnectors(c *gin.Context) {
	user, err := ctrl.getUser(c)
	if err != nil {
		return
	}

	connectors, err := ctrl.repo.GetAllByProjectKey(user.ProjectKey)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, connectors)
}

// GetConnector retrieves a single connector by ID
func (ctrl *ConnectorController) GetConnector(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	connector, err := ctrl.repo.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, connector)
}

// UpdateConnector updates an existing connector
func (ctrl *ConnectorController) UpdateConnector(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var connector models.Connector
	if err := c.ShouldBindJSON(&connector); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	connector.ID = uint(id)
	if err := ctrl.repo.Update(&connector); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, connector)
}

// DeleteConnector deletes a connector by ID
func (ctrl *ConnectorController) DeleteConnector(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := ctrl.repo.Delete(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Connector deleted successfully"})
}
func (ctrl *ConnectorController) ListStaticDropdown(c *gin.Context) {
	typeParam := c.Query("type")
	dropdown := []map[string]string{}

	if typeParam == "Snack" {
		dropdown = []map[string]string{
			{"value": "EVENT_ADD_PAYMENT_INFO", "label": "Add Payment Info"},
			{"value": "EVENT_ADD_TO_CART", "label": "Add to Cart"},
			{"value": "EVENT_BUTTON_CLICK", "label": "Button Click"},
			{"value": "EVENT_PURCHASE", "label": "Purchase"},
			{"value": "EVENT_CONTENT_VIEW", "label": "Content View"},
			{"value": "EVENT_DOWNLOAD", "label": "Download"},
			{"value": "EVENT_FORM_SUBMIT", "label": "Form Submit"},
			{"value": "EVENT_INITIATED_CHECKOUT", "label": "Initiated Checkout"},
			{"value": "EVENT_CONTACT", "label": "Contact"},
			{"value": "EVENT_PLACE_ORDER", "label": "Place an Order"},
			{"value": "EVENT_SEARCH", "label": "Search"},
			{"value": "EVENT_COMPLETE_REGISTRATION", "label": "Complete Registration"},
			{"value": "EVENT_ADD_TO_WISHLIST", "label": "Add to Wishlist"},
			{"value": "EVENT_SUBSCRIBE", "label": "Subscribe"},
			{"value": "EVENT_FIRST_DEPOSIT", "label": "First Deposit"},
			{"value": "EVENT_CREDIT_APPROVAL", "label": "Credit Approval"},
			{"value": "EVENT_LOAN_APPLICATION", "label": "Loan Application"},
			{"value": "EVENT_LOAN_CREDIT", "label": "Loan Credit"},
			{"value": "EVENT_LOAN_DISBURSAL", "label": "Loan Disbursal"},
			{"value": "EVENT_CREDIT_CARD_APPLICATION", "label": "Credit Card Application"},
		}
	} else if typeParam == "Facebook" || typeParam == "MGID" {
		dropdown = []map[string]string{
			{"value": "AddPaymentInfo", "label": "Add Payment Info"},
			{"value": "AddToCart", "label": "Add to Cart"},
			{"value": "AddToWishlist", "label": "Add to Wishlist"},
			{"value": "CompleteRegistration", "label": "Complete Registration"},
			{"value": "Contact", "label": "Contact"},
			{"value": "CustomizeProduct", "label": "Customize Product"},
			{"value": "Donate", "label": "Donate"},
			{"value": "FindLocation", "label": "Find Location"},
			{"value": "InitiateCheckout", "label": "Initiate Checkout"},
			{"value": "Lead", "label": "Lead"},
			{"value": "Purchase", "label": "Purchase"},
			{"value": "Schedule", "label": "Schedule"},
			{"value": "Search", "label": "Search"},
			{"value": "StartTrial", "label": "Start Trial"},
			{"value": "SubmitApplication", "label": "Submit Application"},
			{"value": "Subscribe", "label": "Subscribe"},
			{"value": "ViewContent", "label": "View Content"},
		}
	} else if typeParam == "Tiktok" {
		dropdown = []map[string]string{
			{"value": "AddPaymentInfo", "label": "Add Payment Info"},
			{"value": "AddToCart", "label": "Add to Cart"},
			{"value": "AddToWishlist", "label": "Add to Wishlist"},
			{"value": "ClickButton", "label": "Click Button"},
			{"value": "CompletePayment", "label": "Complete Payment"},
			{"value": "CompleteRegistration", "label": "Complete Registration"},
			{"value": "Contact", "label": "Contact"},
			{"value": "Download", "label": "Download"},
			{"value": "InitiateCheckout", "label": "Initiate Checkout"},
			{"value": "PlaceAnOrder", "label": "Place an Order"},
			{"value": "Search", "label": "Search"},
			{"value": "SubmitForm", "label": "Submit Form"},
			{"value": "Subscribe", "label": "Subscribe"},
			{"value": "ViewContent", "label": "View Content"},
		}
	}

	c.JSON(http.StatusOK, dropdown)
}

func (ctrl *ConnectorController) getUser(c *gin.Context) (*models.User, error) {
	// Get user ID from the context
	currentUser, exists := c.Get("userKey")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User ID not found in context"})
		return nil, fmt.Errorf("User ID not found in context")
	}

	// Retrieve the user from the repository
	user, err := ctrl.repoUser.GetByUserKey(currentUser.(string))
	if err != nil {
		return nil, fmt.Errorf("Failed to retrieve user project key")
	}
	if user == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return nil, fmt.Errorf("User not found")
	}
	if user.ProjectKey == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User project key is required"})
		return nil, fmt.Errorf("User project key is required")
	}

	return user, nil
}