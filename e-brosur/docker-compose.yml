version: "3.9"
services:
  app:
    build:
      dockerfile: Dockerfile
      context: .
    environment:
      DATABASE_URL: "host=db user=postgres password=postgres-e-brochure dbname=e-brochure sslmode=disable"
    command: go run main.go
    ports:
      - "8010:8009"
    develop:
      watch:
        - action: sync+restart
          path: ./
          target: /app
    depends_on:
      - db
    volumes:
      - ./qr-codes:/app/qr-codes
      - ./qr_codes:/app/qr_codes

  db:
    image: postgres:13
    container_name: database
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres-e-brochure
      POSTGRES_DB: e-brochure
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
  # app:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
  #   # env_file:
  #   #   - .env
  #   develop:
  #     watch:
  #       - action: sync+restart
  #         path: ./
  #         target: /app
  #   expose:
  #     - "8010"
  #   environment:
  #     - PORT=8010
  #   restart: always
  #   depends_on:
  #     - db

  # nginx:
  #   image: nginx:latest
  #   volumes:
  #     - ./nginx/nginx.conf:/etc/nginx/conf.d/default.conf
  #   ports:
  #     - "80:80"
  #     - "443:443"
  #   depends_on:
  #     - app
  #   restart: always

  # db:
  #   image: postgres:13
  #   container_name: database
  #   restart: always
  #   environment:
  #     POSTGRES_USER: postgres
  #     POSTGRES_PASSWORD: postgres-e-brochure
  #     POSTGRES_DB: e-brochure
  #   ports:
  #     - "5432:5432"
  #   volumes:
  #     - pgdata:/var/lib/postgresql/data
volumes:
  pgdata:
