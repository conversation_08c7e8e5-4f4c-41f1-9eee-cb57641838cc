package repositories

import (
	"e-brochure/models"

	"gorm.io/gorm"
)

// FormFillRepository defines methods for form fill data management
type FormFillRepository interface {
	Create(formFill *models.FormFill) (*models.FormFill, error)
	GetAll() ([]*models.FormFill, error)
	GetByID(id uint) (*models.FormFill, error)
	Update(formFill *models.FormFill) (*models.FormFill, error)
	Delete(id uint) error
}

type formFillRepository struct {
	db *gorm.DB
}

// NewFormFillRepository creates a new form fill repository
func NewFormFillRepository(db *gorm.DB) FormFillRepository {
	return &formFillRepository{db: db}
}

func (r *formFillRepository) Create(formFill *models.FormFill) (*models.FormFill, error) {
	err := r.db.Create(formFill).Error
	if err != nil {
		return nil, err
	}
	return formFill, nil
}

func (r *formFillRepository) GetAll() ([]*models.FormFill, error) {
	formFills := []*models.FormFill{}
	err := r.db.Find(&formFills).Error
	if err != nil {
		return nil, err
	}
	return formFills, nil
}

func (r *formFillRepository) GetByID(id uint) (*models.FormFill, error) {
	formFill := &models.FormFill{}
	err := r.db.Where("id = ?", id).First(formFill).Error
	if err != nil {
		return nil, err
	}
	return formFill, nil
}

func (r *formFillRepository) Update(formFill *models.FormFill) (*models.FormFill, error) {
	err := r.db.Save(formFill).Error
	if err != nil {
		return nil, err
	}
	return formFill, nil
}

func (r *formFillRepository) Delete(id uint) error {
	formFill := &models.FormFill{}
	err := r.db.Where("id = ?", id).Delete(formFill).Error
	if err != nil {
		return err
	}
	return nil
}
