package repositories

import (
	"e-brochure/models"
	"errors"

	"gorm.io/gorm"
)

// InfluencerRepository defines methods for influencer data management
type InfluencerRepository interface {
	Create(influencer *models.Influencer) error
	GetByID(id uint) (*models.Influencer, error)
	GetByInstagram(instagram string) (*models.Influencer, error)
	GetAll() ([]models.Influencer, error)
	GetAllPaged(page, limit int) ([]models.Influencer, error)
	Update(influencer *models.Influencer) error
	Delete(id uint) error
	Count() (int64, error)
}

type influencerRepository struct {
	db *gorm.DB
}

// NewInfluencerRepository creates a new instance of InfluencerRepository
func NewInfluencerRepository(db *gorm.DB) InfluencerRepository {
	return &influencerRepository{db: db}
}

func (r *influencerRepository) Create(influencer *models.Influencer) error {
	return r.db.Create(influencer).Error
}

func (r *influencerRepository) GetByID(id uint) (*models.Influencer, error) {
	var influencer models.Influencer
	err := r.db.Where("id = ?", id).First(&influencer).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &influencer, nil
}

func (r *influencerRepository) GetByInstagram(instagram string) (*models.Influencer, error) {
	var influencer models.Influencer
	err := r.db.Where("instagram = ?", instagram).First(&influencer).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &influencer, nil
}

func (r *influencerRepository) GetAll() ([]models.Influencer, error) {
	var influencers []models.Influencer
	err := r.db.Find(&influencers).Error
	if err != nil {
		return nil, err
	}
	return influencers, nil
}

func (r *influencerRepository) GetAllPaged(page, limit int) ([]models.Influencer, error) {
	var influencers []models.Influencer
	offset := (page - 1) * limit
	err := r.db.Offset(offset).Limit(limit).Find(&influencers).Error
	if err != nil {
		return nil, err
	}
	return influencers, nil
}

func (r *influencerRepository) Update(influencer *models.Influencer) error {
	return r.db.Save(influencer).Error
}

func (r *influencerRepository) Delete(id uint) error {
	return r.db.Delete(&models.Influencer{}, "id = ?", id).Error
}

func (r *influencerRepository) Count() (int64, error) {
	var count int64
	err := r.db.Model(&models.Influencer{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}
