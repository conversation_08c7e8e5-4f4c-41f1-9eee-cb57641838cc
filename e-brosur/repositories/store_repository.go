package repositories

import (
	"e-brochure/models"
	"errors"
	"time"

	"gorm.io/gorm"
)

// StoreRepository defines methods for store data management
type StoreRepository interface {
	Create(store *models.Store) error
	GetByID(id uint) (*models.Store, error)
	GetAll() ([]models.Store, error)
	GetAllPaged(page, limit int, projectKey string) ([]models.Store, error)
	Update(store *models.Store) error
	Delete(id uint) error
	Count() (int64, error)
	GetByNamePaged(name string, page, limit int, projectKey string) ([]models.Store, error)
	CreateOperationalDay(day *models.OperationalDay) (*models.OperationalDay, error)
	FindStoreByID(id uint) (*models.Store, error)
	FindOperationalDay(day *models.OperationalDay) (*models.OperationalDay, error)
	GetDB() *gorm.DB
	GetByBrochureID(brochureID uint) ([]models.Store, error)
}

// storeRepository implements StoreRepository with a GORM database
type storeRepository struct {
	db *gorm.DB
}

// NewStoreRepository returns a new instance of storeRepository
func NewStoreRepository(db *gorm.DB) StoreRepository {
	return &storeRepository{db: db}
}

// Create inserts a new store record
func (r *storeRepository) Create(store *models.Store) error {
	return r.db.Create(store).Error
}

// GetByID retrieves a store by its ID
func (r *storeRepository) GetByID(id uint) (*models.Store, error) {
	var store models.Store
	if err := r.db.Preload("OperationalDays").First(&store, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &store, nil
}
// GetAll retrieves all stores
func (r *storeRepository) GetAll() ([]models.Store, error) {
	var stores []models.Store
	if err := r.db.Preload("OperationalDays").Find(&stores).Error; err != nil {
		return nil, err
	}
	return stores, nil
}

// Update modifies an existing store record
func (r *storeRepository) Update(store *models.Store) error {
	return r.db.Save(store).Error
}

// Delete removes a store by its ID
func (r *storeRepository) Delete(id uint) error {
	return r.db.Delete(&models.Store{}, id).Error
}

// GetAllPaged retrieves all stores with pagination
func (r *storeRepository) GetAllPaged(page, limit int, projectKey string) ([]models.Store, error) {
	var stores []models.Store
	offset := (page - 1) * limit
	if err := r.db.Where("project_key = ?", projectKey).Preload("OperationalDays").Offset(offset).Limit(limit).Find(&stores).Error; err != nil {
		return nil, err
	}
	return stores, nil
}

func (r *storeRepository) Count() (int64, error) {
	var count int64
	if err := r.db.Model(&models.Store{}).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// GetByNamePaged retrieves stores by name with pagination
func (r *storeRepository) GetByNamePaged(name string, page, limit int, projectKey string) ([]models.Store, error) {
	var stores []models.Store
	offset := (page - 1) * limit
	if err := r.db.Where("name LIKE ? AND project_key = ?", "%"+name+"%", projectKey).Preload("OperationalDays").Offset(offset).Limit(limit).Find(&stores).Error; err != nil {
		return nil, err
	}
	return stores, nil
}

func (r *storeRepository) CreateOperationalDay(day *models.OperationalDay) (*models.OperationalDay, error) {
	if err := r.db.Create(day).Error; err != nil {
		return nil, err
	}
	return day, nil
}

func (r *storeRepository) FindStoreByID(id uint) (*models.Store, error) {
	var store models.Store
	if err := r.db.First(&store, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, err // Return error if store is not found
		}
		return nil, err // Return any other error
	}
	return &store, nil
}
func (r *storeRepository) FindOperationalDay(day *models.OperationalDay) (*models.OperationalDay, error) {
	var existingDay models.OperationalDay
	if err := r.db.Where("store_id = ? AND day = ? AND open_hours = ? AND close_hours = ?", day.StoreID, day.Day, day.OpenHours, day.CloseHours).First(&existingDay).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &existingDay, nil
}
func (r *storeRepository) GetDB() *gorm.DB {
    return r.db
}
func (r *storeRepository) GetByBrochureID(brochureID uint) ([]models.Store, error) {
	var stores []models.Store
	currentDay := time.Now().Weekday().String()
	// var operationalDays []models.OperationalDay
	// if err := r.db.Table("operational_days").Select("*").Find(&operationalDays).Error; err != nil {
	// 	return nil, err
	// }
	// if err := r.db.Table("operational_days").
	// 	Select("operational_days.id, operational_days.store_id, operational_days.day, operational_days.open_hours, operational_days.close_hours").
	// 	Find(&operationalDays).Error; err != nil {
	// 	return nil, err
	// }
	// log.Println("Current day:", currentDay)
	// if err := r.db.Table("brochure_stores").
	// 	Select("od.open_hours, od.close_hours,od.day").
	// 	Joins("INNER JOIN stores s ON s.id = brochure_stores.store_id").
	// 	Where("brochure_stores.brochure_id = ?", brochureID).
	// 	Joins("LEFT JOIN operational_days od ON s.id = od.store_id AND od.day = ?", currentDay).
	// 	Find(&stores).Error; err != nil {
	// 	return nil, err
	// }
	if err := r.db.Table("stores s").
		Select("s.*").
		Joins("INNER JOIN brochure_stores bs ON bs.store_id = s.id").
		Where("bs.brochure_id = ?", brochureID).
		Preload("OperationalDays", "day = ?", currentDay).
		Find(&stores).Error; err != nil {
		return nil, err
	}
	return stores, nil
}
