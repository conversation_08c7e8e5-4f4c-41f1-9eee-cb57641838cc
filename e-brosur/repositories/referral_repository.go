package repositories

import (
	"e-brochure/models"

	"gorm.io/gorm"
)

// ReferralRepository is the repository for managing referrals
type ReferralRepository struct {
	db *gorm.DB
}

// NewReferralRepository creates a new ReferralRepository
func NewReferralRepository(db *gorm.DB) *ReferralRepository {
	return &ReferralRepository{db: db}
}

// CreateReferral creates a new referral in the database
func (repo *ReferralRepository) CreateReferral(referral *models.Referral) error {
	if err := repo.db.Create(referral).Error; err != nil {
		return err
	}
	return nil
}
// GetAffiliateByCode fetches an affiliate by its Code
func (r *ReferralRepository) GetAffiliateByCode(code string) (*models.Affiliate, error) {
	var affiliate models.Affiliate
	if err := r.db.Where("code = ?", code).First(&affiliate).Error; err != nil {
		return nil, err
	}
	return &affiliate, nil
}

// GetReferralWithAffiliate fetches a single referral, including its associated Affiliate
func (r *ReferralRepository) GetReferralWithAffiliate(id uint) (*models.Referral, error) {
	var referral models.Referral
	if err := r.db.Preload("Affiliate").First(&referral, id).Error; err != nil {
		return nil, err
	}
	return &referral, nil
}

// GetAffiliateByID fetches an affiliate by its ID
func (r *ReferralRepository) GetAffiliateByID(id uint) (*models.Affiliate, error) {
	var affiliate models.Affiliate
	if err := r.db.First(&affiliate, id).Error; err != nil {
		return nil, err
	}
	return &affiliate, nil
}

// GetReferrals fetches all referrals, including their associated Affiliates
func (r *ReferralRepository) GetReferrals() ([]models.Referral, error) {
	var referrals []models.Referral
	// Preload the associated Affiliate model
	if err := r.db.Preload("Affiliate").Find(&referrals).Error; err != nil {
		return nil, err
	}
	return referrals, nil
}
// GetReferralByID fetches a single referral, including its associated Affiliate
func (r *ReferralRepository) GetReferralByID(id uint) (*models.Referral, error) {
	var referral models.Referral
	if err := r.db.Preload("Affiliate").First(&referral, id).Error; err != nil {
		return nil, err
	}
	return &referral, nil
}
// DeleteReferral deletes a referral by its ID
func (r *ReferralRepository) DeleteReferral(id uint) error {
	// Try to delete the referral
	if err := r.db.Delete(&models.Referral{}, id).Error; err != nil {
		return err
	}
	return nil
}
