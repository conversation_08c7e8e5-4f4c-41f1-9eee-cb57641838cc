package repositories

import (
	"e-brochure/models"
	"errors"

	"gorm.io/gorm"
)

// ProvinceRepository defines methods for province data management
type ProvinceRepository interface {
	Create(province *models.Province) error
	GetByID(id uint) (*models.Province, error)
	Update(province *models.Province) error
	Delete(id uint) error
	GetAll() ([]models.Province, error)
}

// provinceRepository implements ProvinceRepository with a GORM database
type provinceRepository struct {
	db *gorm.DB
}

// NewProvinceRepository returns a new instance of provinceRepository
func NewProvinceRepository(db *gorm.DB) ProvinceRepository {
	return &provinceRepository{db: db}
}

func (r *provinceRepository) Create(province *models.Province) error {
	return r.db.Create(province).Error
}

func (r *provinceRepository) GetByID(id uint) (*models.Province, error) {
	var province models.Province
	if err := r.db.First(&province, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &province, nil
}

func (r *provinceRepository) Update(province *models.Province) error {
	return r.db.Save(province).Error
}

func (r *provinceRepository) Delete(id uint) error {
	return r.db.Delete(&models.Province{}, id).Error
}

func (r *provinceRepository) GetAll() ([]models.Province, error) {
	var provinces []models.Province
	if err := r.db.Find(&provinces).Error; err != nil {
		return nil, err
	}
	return provinces, nil
}
