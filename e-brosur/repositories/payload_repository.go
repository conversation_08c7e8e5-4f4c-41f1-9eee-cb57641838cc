package repositories

import (
	"e-brochure/models"
	"errors"

	"gorm.io/gorm"
)

// PayloadRepository defines methods for payload data management
type PayloadRepository interface {
	Create(payload *models.Payload) error
	GetByID(id uint) (*models.Payload, error)
}

type payloadRepository struct {
	db *gorm.DB
}

// NewPayloadRepository returns a new instance of payloadRepository
func NewPayloadRepository(db *gorm.DB) PayloadRepository {
	return &payloadRepository{db: db}
}

func (r *payloadRepository) Create(payload *models.Payload) error {
	return r.db.Create(payload).Error
}

func (r *payloadRepository) GetByID(id uint) (*models.Payload, error) {
	var payload models.Payload
	if err := r.db.First(&payload, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &payload, nil
}
