package repositories

import (
	"e-brochure/models"

	"gorm.io/gorm"
)

type SettingRepository interface {
    FindAll() ([]models.Setting, error)
    FindByID(id uint) (models.Setting, error)
    Create(setting models.Setting) error
    Update(setting models.Setting) error
    Delete(id uint) error
	GetAllPaged(page, limit int) ([]models.Setting, error)
	Count() (int64, error)
	GetByProjectKey(projectKey string) (*models.Setting, error)
    
}

type settingRepository struct {
    db *gorm.DB
}

func NewSettingRepository(db *gorm.DB) SettingRepository {
    return &settingRepository{db}
}

func (r *settingRepository) FindAll() ([]models.Setting, error) {
    var setting []models.Setting
    err := r.db.Find(&setting).Error
    return setting, err
}

func (r *settingRepository) FindByID(id uint) (models.Setting, error) {
    var setting models.Setting
    err := r.db.First(&setting, id).Error
    return setting, err
}

func (r *settingRepository) Create(setting models.Setting) error {
    return r.db.Create(&setting).Error
}

func (r *settingRepository) Update(setting models.Setting) error {
    return r.db.Save(&setting).Error
}

func (r *settingRepository) Delete(id uint) error {
    return r.db.Delete(&models.Setting{}, id).Error
}
func (r *settingRepository) GetAllPaged(page, limit int) ([]models.Setting, error) {
	var setting []models.Setting
	offset := (page - 1) * limit
	if err := r.db.Offset(offset).Limit(limit).Preload("Setting").Find(&setting).Error; err != nil {
		return nil, err
	}
	return setting, nil
}
func (r *settingRepository) Count() (int64, error) {
	var count int64
	if err := r.db.Model(&models.Setting{}).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}
func (r *settingRepository) GetByProjectKey(projectKey string) (*models.Setting, error) {
	var setting models.Setting
	err := r.db.Where("project_key = ?", projectKey).First(&setting).Error
	return &setting, err
}
