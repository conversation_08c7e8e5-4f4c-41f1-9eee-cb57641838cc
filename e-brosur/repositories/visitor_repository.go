package repositories

import (
	"e-brochure/models"

	"gorm.io/gorm"
)

type VisitorRepository interface {
    FindAll() ([]models.Visitor, error)
    FindByID(id uint) (models.Visitor, error)
    Create(visitor models.Visitor) (uint, error)
    Update(visitor models.Visitor) error
    Delete(id uint) error
    GetByVisitorID(visitorID string) ([]models.Visitor, error)
    
}

type visitorRepository struct {
    db *gorm.DB
}

func NewVisitorRepository(db *gorm.DB) VisitorRepository {
    return &visitorRepository{db}
}

func (r *visitorRepository) FindAll() ([]models.Visitor, error) {
    var visitors []models.Visitor
    err := r.db.Preload("Brochure").Find(&visitors).Error
    return visitors, err
}

func (r *visitorRepository) FindByID(id uint) (models.Visitor, error) {
    var visitor models.Visitor
    err := r.db.Preload("Brochure").First(&visitor, id).Error
    return visitor, err
}

func (r *visitorRepository) Create(visitor models.Visitor) (uint, error) {
    err := r.db.Create(&visitor).Error
    if err != nil {
        return 0, err
    }
    return visitor.ID, nil
}

func (r *visitorRepository) Update(visitor models.Visitor) error {
    return r.db.Save(&visitor).Error
}

func (r *visitorRepository) Delete(id uint) error {
    return r.db.Delete(&models.Visitor{}, id).Error
}
func (r *visitorRepository) GetByVisitorID(visitorID string) ([]models.Visitor, error) {
    var visitors []models.Visitor
    err := r.db.Where("visitor_id = ?", visitorID).Preload("Brochure").Find(&visitors).Error
    return visitors, err
}

