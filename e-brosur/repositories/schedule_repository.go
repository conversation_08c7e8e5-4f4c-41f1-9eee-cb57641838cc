package repositories

import (
	"e-brochure/models"
	"time"

	"gorm.io/gorm"
)

// ScheduleRepository defines methods for schedule data management
type ScheduleRepository interface {
	Create(schedule *models.Schedule) error
	GetByID(id uint) (*models.Schedule, error)
	GetByInfluencerID(influencerID uint) ([]models.Schedule, error)
	GetByCampaignID(campaignID uint) ([]models.Schedule, error)
	GetByDate(date time.Time) ([]models.Schedule, error)
	GetByPlatform(platform string) ([]models.Schedule, error)
	Update(schedule *models.Schedule) error
	Delete(id uint) error
	GetAll() ([]models.Schedule, error)
}

// scheduleRepository implements ScheduleRepository with a GORM database
type scheduleRepository struct {
	db *gorm.DB
}

func NewScheduleRepository(db *gorm.DB) ScheduleRepository {
	return &scheduleRepository{db: db}
}

func (r *scheduleRepository) Create(schedule *models.Schedule) error {
	return r.db.Create(schedule).Error
}

func (r *scheduleRepository) GetByID(id uint) (*models.Schedule, error) {
	var schedule models.Schedule
	if err := r.db.Where("id = ?", id).First(&schedule).Error; err != nil {
		return nil, err
	}
	return &schedule, nil
}

func (r *scheduleRepository) GetByInfluencerID(influencerID uint) ([]models.Schedule, error) {
	var schedules []models.Schedule
	if err := r.db.Where("influencer_id = ?", influencerID).Find(&schedules).Error; err != nil {
		return nil, err
	}
	return schedules, nil
}

func (r *scheduleRepository) GetByCampaignID(campaignID uint) ([]models.Schedule, error) {
	var schedules []models.Schedule
	if err := r.db.Where("campaign_id = ?", campaignID).Find(&schedules).Error; err != nil {
		return nil, err
	}
	return schedules, nil
}

func (r *scheduleRepository) GetByDate(date time.Time) ([]models.Schedule, error) {
	var schedules []models.Schedule
	if err := r.db.Where("date = ?", date.Format("2006-01-02")).Find(&schedules).Error; err != nil {
		return nil, err
	}
	return schedules, nil
}

func (r *scheduleRepository) GetByPlatform(platform string) ([]models.Schedule, error) {
	var schedules []models.Schedule
	if err := r.db.Where("platform = ?", platform).Find(&schedules).Error; err != nil {
		return nil, err
	}
	return schedules, nil
}

func (r *scheduleRepository) Update(schedule *models.Schedule) error {
	return r.db.Save(schedule).Error
}

func (r *scheduleRepository) Delete(id uint) error {
	return r.db.Delete(&models.Schedule{}, "id = ?", id).Error
}
func (r *scheduleRepository) GetAll() ([]models.Schedule, error) {
	var schedules []models.Schedule
	if err := r.db.Find(&schedules).Error; err != nil {
		return nil, err
	}
	return schedules, nil
}
