package repositories

import (
	"e-brochure/models"

	"gorm.io/gorm"
)

// ConnectorLogRepository defines methods for connector log data management
type ConnectorLogRepository interface {
	Create(log *models.ConnectorLog) error
	GetByID(id uint) (*models.ConnectorLog, error)
	GetAll() ([]models.ConnectorLog, error)
	Delete(id uint) error
}

// connectorLogRepository implements ConnectorLogRepository with a GORM database
type connectorLogRepository struct {
	db *gorm.DB
}

// NewConnectorLogRepository returns a new instance of ConnectorLogRepository
func NewConnectorLogRepository(db *gorm.DB) ConnectorLogRepository {
	return &connectorLogRepository{db: db}
}

// Create creates a new connector log
func (repo *connectorLogRepository) Create(log *models.ConnectorLog) error {
	return repo.db.Create(log).Error
}

// GetByID returns a connector log by ID
func (repo *connectorLogRepository) GetByID(id uint) (*models.ConnectorLog, error) {
	var log models.ConnectorLog
	err := repo.db.First(&log, id).Error
	return &log, err
}

// GetAll returns all connector logs
func (repo *connectorLogRepository) GetAll() ([]models.ConnectorLog, error) {
	var logs []models.ConnectorLog
	err := repo.db.Find(&logs).Error
	return logs, err
}

// Delete deletes a connector log
func (repo *connectorLogRepository) Delete(id uint) error {
	return repo.db.Delete(&models.ConnectorLog{}, id).Error
}

