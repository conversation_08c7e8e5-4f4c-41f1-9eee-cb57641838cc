package repositories

import (
	"e-brochure/models"

	"gorm.io/gorm"
)

// ConnectorRepository defines methods for connector data management
type ConnectorRepository interface {
	Create(connector *models.Connector) error
	GetByID(id uint) (*models.Connector, error)
	GetAll() ([]models.Connector, error)
	GetAllPaged(page, limit int) ([]models.Connector, error)
	Update(connector *models.Connector) error
	Delete(id uint) error
	Count() (int64, error)
	GetAllByProjectKey(projectKey string) ([]models.Connector, error)
}

// connectorRepository implements ConnectorRepository with a GORM database
type connectorRepository struct {
	db *gorm.DB
}

// NewConnectorRepository returns a new instance of ConnectorRepository
func NewConnectorRepository(db *gorm.DB) ConnectorRepository {
	return &connectorRepository{db: db}
}

// Create creates a new connector
func (repo *connectorRepository) Create(connector *models.Connector) error {
	return repo.db.Create(connector).Error
}

// GetByID returns a connector by ID
func (repo *connectorRepository) GetByID(id uint) (*models.Connector, error) {
	var connector models.Connector
	if err := repo.db.Where("id = ?", id).First(&connector).Error; err != nil {
		return nil, err
	}
	return &connector, nil
}

// GetAll returns all connectors
func (repo *connectorRepository) GetAll() ([]models.Connector, error) {
	var connectors []models.Connector
	if err := repo.db.Find(&connectors).Error; err != nil {
		return nil, err
	}
	return connectors, nil
}

// GetAllPaged returns all connectors with pagination
func (repo *connectorRepository) GetAllPaged(page, limit int) ([]models.Connector, error) {
	var connectors []models.Connector
	offset := (page - 1) * limit
	if err := repo.db.Offset(offset).Limit(limit).Find(&connectors).Error; err != nil {
		return nil, err
	}
	return connectors, nil
}

// Update updates a connector
func (repo *connectorRepository) Update(connector *models.Connector) error {
	return repo.db.Save(connector).Error
}

// Delete deletes a connector
func (repo *connectorRepository) Delete(id uint) error {
	return repo.db.Delete(&models.Connector{}, "id = ?", id).Error
}

// Count returns the number of connectors
func (repo *connectorRepository) Count() (int64, error) {
	var count int64
	if err := repo.db.Model(&models.Connector{}).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}
// GetAllByProjectKey returns all connectors by project key
func (repo *connectorRepository) GetAllByProjectKey(projectKey string) ([]models.Connector, error) {
	var connectors []models.Connector
	if err := repo.db.Where("project_key = ?", projectKey).Find(&connectors).Error; err != nil {
		return nil, err
	}
	return connectors, nil
}
