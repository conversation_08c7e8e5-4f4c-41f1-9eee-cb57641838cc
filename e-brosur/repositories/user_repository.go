package repositories

import (
	"e-brochure/models"
	"errors"

	"gorm.io/gorm"
)

// UserRepository defines methods for user data management
type UserRepository interface {
	Create(user *models.User) error
	GetByID(id uint) (*models.User, error)
	GetByUserKey(userKey string) (*models.User, error)
	GetByEmail(email string) (*models.User, error)
	Update(user *models.User) error
	Delete(id uint) error
	GetProjectsByUserID(userID uint) ([]models.Project, error)
	GetByPhone(phone string) (*models.User, error)
	GetAllByParentID(parentID uint) ([]models.User, error)
}

// userRepository implements UserRepository with a GORM database
type userRepository struct {
	db *gorm.DB
}

// NewUserRepository returns a new instance of userRepository
func NewUserRepository(db *gorm.DB) UserRepository {
	return &userRepository{db: db}
}

func (r *userRepository) Create(user *models.User) error {
	return r.db.Create(user).Error
}

func (r *userRepository) GetByID(id uint) (*models.User, error) {
	var user models.User
	if err := r.db.First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

func (r *userRepository) GetByEmail(email string) (*models.User, error) {
	var user models.User
	if err := r.db.Where("email = ?", email).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

func (r *userRepository) Update(user *models.User) error {
	return r.db.Save(user).Error
}

func (r *userRepository) Delete(id uint) error {
	return r.db.Delete(&models.User{}, id).Error
}

func (r *userRepository) GetByUserKey(userKey string) (*models.User, error) {
	var user models.User
	if err := r.db.Where("user_key = ?", userKey).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

func (r *userRepository) GetProjectsByUserID(userID uint) ([]models.Project, error) {
	var projects []models.Project
	if err := r.db.Where("user_id = ?", userID).Find(&projects).Error; err != nil {
		return nil, err
	}
	return projects, nil
}

func (r *userRepository) GetByPhone(phone string) (*models.User, error) {
	var user models.User
	if err := r.db.Where("phone = ?", phone).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}
func (r *userRepository) GetAllByParentID(parentID uint) ([]models.User, error) {
	var users []models.User
	if err := r.db.Where("parent_id = ?", parentID).Find(&users).Error; err != nil {
		return nil, err
	}
	return users, nil
}
