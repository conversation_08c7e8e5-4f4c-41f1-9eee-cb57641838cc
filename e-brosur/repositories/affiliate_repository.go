package repositories

import (
	"e-brochure/models"
	"gorm.io/gorm"
)

// AffiliateRepository defines methods for budget data management
type AffiliateRepository interface {
	GetAffiliates() ([]models.Affiliate, error)
	CreateAffiliate(affiliate *models.Affiliate) error
	GetAffiliateByCode(code string) (*models.Affiliate, error)
	GetAffiliate(id uint) (*models.Affiliate, error)
	UpdateAffiliate(affiliate *models.Affiliate) error
	DeleteAffiliate(id uint) error
}
type affiliateRepository struct {
	db *gorm.DB
}

func NewAffiliateRepository(db *gorm.DB) AffiliateRepository {
	return &affiliateRepository{db: db}
}
func (ar *affiliateRepository) CreateAffiliate(affiliate *models.Affiliate) error {
	return ar.db.Create(affiliate).Error
}
func (ar *affiliateRepository) GetAffiliate(id uint) (*models.Affiliate, error) {
	var affiliate models.Affiliate
	err := ar.db.First(&affiliate, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &affiliate, nil
}

// GetAffiliateByCode checks if an affiliate exists by code
func (ar *affiliateRepository) GetAffiliateByCode(code string) (*models.Affiliate, error) {
	var affiliate models.Affiliate
	err := ar.db.First(&affiliate, "code = ?", code).Error
	if err != nil {
		if err.Error() == "record not found" {
			// Return nil if no affiliate is found
			return nil, nil
		}
		return nil, err // Return any other errors (e.g., database issues)
	}
	return &affiliate, nil
}

func (ar *affiliateRepository) GetAffiliates() ([]models.Affiliate, error) {
	var affiliates []models.Affiliate
	err := ar.db.Find(&affiliates).Error
	if err != nil {
		return nil, err
	}
	return affiliates, nil
}

func (ar *affiliateRepository) UpdateAffiliate(affiliate *models.Affiliate) error {
	return ar.db.Save(affiliate).Error
}
func (ar *affiliateRepository) DeleteAffiliate(id uint) error {
	return ar.db.Delete(&models.Affiliate{}, "id = ?", id).Error
}
