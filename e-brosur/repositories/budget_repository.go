package repositories

import (
	"e-brochure/models"

	"gorm.io/gorm"
)

// BudgetRepository defines methods for budget data management
type BudgetRepository interface {
	GetAll() ([]models.Budget, error)
	Create(budget *models.Budget) error
	GetByID(id uint) (*models.Budget, error)
	GetByUserID(userID uint) ([]models.Budget, error)
	Update(budget *models.Budget) error
	Delete(id uint) error
}

type budgetRepository struct {
	db *gorm.DB
}

func NewBudgetRepository(db *gorm.DB) BudgetRepository {
	return &budgetRepository{db: db}
}

func (br *budgetRepository) Create(budget *models.Budget) error {
	return br.db.Create(budget).Error
}

func (br *budgetRepository) GetByID(id uint) (*models.Budget, error) {
	var budget models.Budget
	err := br.db.First(&budget, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &budget, nil
}

func (br *budgetRepository) GetByUserID(userID uint) ([]models.Budget, error) {
	var budgets []models.Budget
	err := br.db.Where("user_id = ?", userID).Find(&budgets).Error
	if err != nil {
		return nil, err
	}
	return budgets, nil
}

func (br *budgetRepository) Update(budget *models.Budget) error {
	return br.db.Save(budget).Error
}

func (br *budgetRepository) Delete(id uint) error {
	return br.db.Delete(&models.Budget{}, "id = ?", id).Error
}
func (br *budgetRepository) GetAll() ([]models.Budget, error) {
	var budgets []models.Budget
	err := br.db.Find(&budgets).Error
	if err != nil {
		return nil, err
	}
	return budgets, nil
}
