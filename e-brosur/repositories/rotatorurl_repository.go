package repositories

import (
	"e-brochure/models"
	"errors"

	"gorm.io/gorm"
)

// RotatorURLRepository defines methods for RotatorURL data management
type RotatorURLRepository interface {
	Create(rotator *models.RotatorURL) error
	GetByID(id uint) (*models.RotatorURL, error)
	GetAll() ([]models.RotatorURL, error)
	GetAllPaged(page, limit int, projectKey string) ([]models.RotatorURL, error)
	Update(rotator *models.RotatorURL) error
	Delete(id uint) error
	Count(projectKey string) (int64, error)
	GetByShortURL(shortURL string) (*models.RotatorURL, error)
}

// rotatorURLRepository implements RotatorURLRepository with a GORM database
type rotatorURLRepository struct {
	db *gorm.DB
}

// NewRotatorURLRepository returns a new instance of rotatorURLRepository
func NewRotatorURLRepository(db *gorm.DB) RotatorURLRepository {
	return &rotatorURLRepository{db: db}
}

// Create inserts a new RotatorURL record
func (r *rotatorURLRepository) Create(rotator *models.RotatorURL) error {
	return r.db.Create(rotator).Error
}

// GetByID retrieves a RotatorURL by its ID
func (r *rotatorURLRepository) GetByID(id uint) (*models.RotatorURL, error) {
	var rotator models.RotatorURL
	if err := r.db.Preload("Brochures").First(&rotator, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &rotator, nil
}

// GetAll retrieves all RotatorURLs
func (r *rotatorURLRepository) GetAll() ([]models.RotatorURL, error) {
	var rotators []models.RotatorURL
	if err := r.db.Preload("Brochures").Find(&rotators).Error; err != nil {
		return nil, err
	}
	return rotators, nil
}

// Update modifies an existing RotatorURL record
func (r *rotatorURLRepository) Update(rotator *models.RotatorURL) error {
	// Begin a new transaction
	tx := r.db.Begin()

	// Fetch the existing RotatorURL to ensure we are working with the correct entity
	var existingRotator models.RotatorURL
	if err := tx.Preload("Brochures").First(&existingRotator, rotator.ID).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Clear existing brochure associations to remove any previous links
	if err := tx.Model(&existingRotator).Association("Brochures").Clear(); err != nil {
		tx.Rollback()
		return err
	}

	// Update the fields of the existing rotator
	existingRotator.Impression = rotator.Impression
	existingRotator.Click = rotator.Click
	existingRotator.Conversion = rotator.Conversion
	existingRotator.ShortURL = rotator.ShortURL

	// Save the updated rotator
	if err := tx.Save(&existingRotator).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Re-associate with updated brochures if needed
	if len(rotator.Brochures) > 0 {
		if err := tx.Model(&existingRotator).Association("Brochures").Append(&rotator.Brochures); err != nil {
			tx.Rollback()
			return err
		}
	}

	// Commit the transaction if everything succeeds
	return tx.Commit().Error
}
// Delete removes a RotatorURL by its ID
func (r *rotatorURLRepository) Delete(id uint) error {
    // Begin a new transaction
    tx := r.db.Begin()

    // Fetch the existing RotatorURL including its associated brochures
    var rotator models.RotatorURL
    if err := tx.Preload("Brochures").First(&rotator, id).Error; err != nil {
        tx.Rollback()
        return err
    }

    // Clear the existing brochures association to avoid foreign key constraint issues
    if err := tx.Model(&rotator).Association("Brochures").Clear(); err != nil {
        tx.Rollback()
        return err
    }

    // Now delete the RotatorURL
    if err := tx.Delete(&rotator).Error; err != nil {
        tx.Rollback()
        return err
    }

    // Commit the transaction if everything succeeds
    return tx.Commit().Error
}
// GetAllPaged retrieves all RotatorURLs with pagination
func (r *rotatorURLRepository) GetAllPaged(page, limit int, projectKey string) ([]models.RotatorURL, error) {
	var rotators []models.RotatorURL
	offset := (page - 1) * limit
	if err := r.db.Where("project_key = ?", projectKey).Offset(offset).Limit(limit).Preload("Brochures").Find(&rotators).Error; err != nil {
		return nil, err
	}
	return rotators, nil
}
func (r *rotatorURLRepository) Count(projectKey string) (int64, error) {
	var count int64
	if err := r.db.Model(&models.RotatorURL{}).Where("project_key = ?", projectKey).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}
// GetByShortURL retrieves a RotatorURL by its short URL
func (r *rotatorURLRepository) GetByShortURL(shortURL string) (*models.RotatorURL, error) {
    var rotator models.RotatorURL
    if err := r.db.Where("short_url = ?", shortURL).Preload("Brochures").First(&rotator).Error; err != nil {
        return nil, err
    }
    return &rotator, nil
}
