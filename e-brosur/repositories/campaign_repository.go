package repositories

import (
	"e-brochure/models"

	"gorm.io/gorm"
)

// CampaignRepository defines methods for campaign data management
type CampaignRepository interface {
	Create(campaign *models.Campaign) error
	GetByID(id uint) (*models.Campaign, error)
	Update(campaign *models.Campaign) error
	Delete(id uint) error
	GetAll() ([]models.Campaign, error)
	AssignInfluencerToCampaign(campaign *models.Campaign, influencer *models.Influencer) error
}

type campaignRepository struct {
	db *gorm.DB
}

// NewCampaignRepository returns a new instance of campaignRepository
func NewCampaignRepository(db *gorm.DB) CampaignRepository {
	return &campaignRepository{db: db}
}

func (r *campaignRepository) Create(campaign *models.Campaign) error {
	return r.db.Create(campaign).Error
}

func (r *campaignRepository) GetByID(id uint) (*models.Campaign, error) {
	var campaign models.Campaign
	if err := r.db.First(&campaign, id).Error; err != nil {
		return nil, err
	}
	return &campaign, nil
}

func (r *campaignRepository) Update(campaign *models.Campaign) error {
	return r.db.Save(campaign).Error
}

func (r *campaignRepository) Delete(id uint) error {
	return r.db.Delete(&models.Campaign{}, id).Error
}

func (r *campaignRepository) GetAll() ([]models.Campaign, error) {
	var campaigns []models.Campaign
	if err := r.db.Find(&campaigns).Error; err != nil {
		return nil, err
	}
	return campaigns, nil
}
func (r *campaignRepository) AssignInfluencerToCampaign(campaign *models.Campaign, influencer *models.Influencer) error {
    return r.db.Model(campaign).Association("Influencers").Append(influencer)
}
