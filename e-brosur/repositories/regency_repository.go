package repositories

import (
	"e-brochure/models"

	"gorm.io/gorm"
)

// RegencyRepository defines methods for regency data management
type RegencyRepository interface {
	Create(regency *models.Regency) error
	GetAll() ([]models.Regency, error)
	GetByID(id uint) (*models.Regency, error)
	Update(regency *models.Regency) error
	Delete(id uint) error
	GetByName(name string) ([]models.Regency, error)
}

type regencyRepository struct {
	db *gorm.DB
}

func NewRegencyRepository(db *gorm.DB) RegencyRepository {
	return &regencyRepository{db: db}
}

func (r *regencyRepository) Create(regency *models.Regency) error {
	result := r.db.Create(regency)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (r *regencyRepository) GetAll() ([]models.Regency, error) {
	var regencies []models.Regency
	result := r.db.Find(&regencies)
	if result.Error != nil {
		return nil, result.Error
	}
	return regencies, nil
}

func (r *regencyRepository) GetByID(id uint) (*models.Regency, error) {
	var regency models.Regency
	result := r.db.First(&regency, id)
	if result.Error != nil {
		return nil, result.Error
	}
	return &regency, nil
}

func (r *regencyRepository) Update(regency *models.Regency) error {
	result := r.db.Save(regency)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (r *regencyRepository) Delete(id uint) error {
	result := r.db.Delete(&models.Regency{}, id)
	if result.Error != nil {
		return result.Error
	}
	return nil
}
func (r *regencyRepository) GetByName(name string) ([]models.Regency, error) {
	var regencies []models.Regency
	result := r.db.Where("LOWER(name) LIKE LOWER(?)", "%"+name+"%").Find(&regencies)
	if result.Error != nil {
		return nil, result.Error
	}
	return regencies, nil
}
