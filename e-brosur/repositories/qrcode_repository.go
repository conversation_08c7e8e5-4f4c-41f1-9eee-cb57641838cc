package repositories

import (
	"e-brochure/models"
	"errors"

	"gorm.io/gorm"
)

// QrCodeRepository defines methods for qrcode data management
type QrCodeRepository interface {
	Create(qrCode *models.QRCode) error
	GetByID(id uint) (*models.QRCode, error)
	GetByProjectID(projectID uint) ([]models.QRCode, error)
	GetByCode(code string) (*models.QRCode, error)
	Update(QRCode *models.QRCode) error
	Delete(id uint) error
	GetAll() ([]models.QRCode, error)
	GetByDefaultMessage(defaultMessage string) (*models.QRCode, error)
}

// qrCodeRepository implements QrCodeRepository with a GORM database
type qrCodeRepository struct {
	db *gorm.DB
}

func NewQrCodeRepository(db *gorm.DB) QrCodeRepository {
	return &qrCodeRepository{db: db}
}

func (qr qrCodeRepository) Create(qrCode *models.QRCode) error {
	return qr.db.Create(qrCode).Error
}

func (qr qrCodeRepository) GetByID(id uint) (*models.QRCode, error) {
	var QRCode models.QRCode
	if err := qr.db.Where("id = ?", id).First(&QRCode).Error; err != nil {
		return nil, errors.New("qrcode not found")
	}
	return &QRCode, nil
}

func (qr qrCodeRepository) GetByProjectID(projectID uint) ([]models.QRCode, error) {
	var qrCodes []models.QRCode
	if err := qr.db.Where("project_id = ?", projectID).Find(&qrCodes).Error; err != nil {
		return nil, errors.New("qrcodes not found")
	}
	return qrCodes, nil
}

func (qr qrCodeRepository) GetByCode(code string) (*models.QRCode, error) {
	var qrCode models.QRCode
	if err := qr.db.Where("code = ?", code).First(&qrCode).Error; err != nil {
		return nil, errors.New("qrcode not found")
	}
	return &qrCode, nil
}

func (qr qrCodeRepository) Update(qrCode *models.QRCode) error {
	return qr.db.Save(qrCode).Error
}

func (qr qrCodeRepository) Delete(id uint) error {
	return qr.db.Delete(&models.QRCode{}, "id = ?", id).Error
}

func (qr qrCodeRepository) GetAll() ([]models.QRCode, error) {
	var qrCodes []models.QRCode
	if err := qr.db.Find(&qrCodes).Error; err != nil {
		return nil, errors.New("qrcodes not found")
	}
	return qrCodes, nil
}

func (qr qrCodeRepository) GetByDefaultMessage(defaultMessage string) (*models.QRCode, error) {
	var qrCode models.QRCode
	if err := qr.db.Where("default_message = ?", defaultMessage).First(&qrCode).Error; err != nil {
		return nil, errors.New("qrcode not found")
	}
	return &qrCode, nil
}

