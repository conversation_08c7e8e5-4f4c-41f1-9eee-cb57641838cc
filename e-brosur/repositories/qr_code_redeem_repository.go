package repositories

import (
	"e-brochure/models"
	"errors"

	"gorm.io/gorm"
)

// QRCodeRedeemRepository defines methods for qr_code_redeem data management
type QRCodeRedeemRepository interface {
	Create(qrCodeRedeem *models.QRCodeRedeem) error
	GetByQRCodeID(qrCodeID uint) ([]models.QRCodeRedeem, error)
	GetByVisitorID(visitorID uint) ([]models.QRCodeRedeem, error)
	GetByID(id uint) (*models.QRCodeRedeem, error)
	Update(qrCodeRedeem *models.QRCodeRedeem) error
	GetAll() ([]models.QRCodeRedeem, error)
	Delete(id uint) error
	GetByStoreID(storeID uint) ([]models.QRCodeRedeem, error)
}

// qrCodeRedeemRepository implements QRCodeRedeemRepository with a GORM database
type qrCodeRedeemRepository struct {
	db *gorm.DB
}

// NewQRCodeRedeemRepository creates a new QRCodeRedeemRepository with the given database
func NewQRCodeRedeemRepository(db *gorm.DB) QRCodeRedeemRepository {
	return &qrCodeRedeemRepository{db: db}
}

func (qr qrCodeRedeemRepository) Create(qrCodeRedeem *models.QRCodeRedeem) error {
	return qr.db.Create(qrCodeRedeem).Error
}

func (qr qrCodeRedeemRepository) GetByQRCodeID(qrCodeID uint) ([]models.QRCodeRedeem, error) {
	var qrCodeRedeems []models.QRCodeRedeem
	if err := qr.db.Where("qr_code_id = ?", qrCodeID).Find(&qrCodeRedeems).Error; err != nil {
		return nil, errors.New("qrcoderedeems not found")
	}
	return qrCodeRedeems, nil
}

func (qr qrCodeRedeemRepository) GetByVisitorID(visitorID uint) ([]models.QRCodeRedeem, error) {
	var qrCodeRedeems []models.QRCodeRedeem
	if err := qr.db.Where("visitor_id = ?", visitorID).Find(&qrCodeRedeems).Error; err != nil {
		return nil, errors.New("qrcoderedeems not found")
	}
	return qrCodeRedeems, nil
}

func (qr qrCodeRedeemRepository) GetByID(id uint) (*models.QRCodeRedeem, error) {
	var qrCodeRedeem models.QRCodeRedeem
	if err := qr.db.First(&qrCodeRedeem, id).Error; err != nil {
		return nil, errors.New("qrcoderedeem not found")
	}
	return &qrCodeRedeem, nil
}

func (qr qrCodeRedeemRepository) Update(qrCodeRedeem *models.QRCodeRedeem) error {
	return qr.db.Save(qrCodeRedeem).Error
}

func (qr qrCodeRedeemRepository) GetAll() ([]models.QRCodeRedeem, error) {
	var qrCodeRedeems []models.QRCodeRedeem
	if err := qr.db.Preload("QRCode").Preload("VisitorProfile").Find(&qrCodeRedeems).Error; err != nil {
		return nil, errors.New("qrcoderedeems not found")
	}
	return qrCodeRedeems, nil
}

func (qr qrCodeRedeemRepository) Delete(id uint) error {
	return qr.db.Delete(&models.QRCodeRedeem{}, "id = ?", id).Error
}

func (qr qrCodeRedeemRepository) GetByStoreID(storeID uint) ([]models.QRCodeRedeem, error) {
	var qrCodeRedeems []models.QRCodeRedeem
	if err := qr.db.Where("store_id = ?", storeID).Find(&qrCodeRedeems).Error; err != nil {
		return nil, errors.New("qrcoderedeems not found")
	}
	return qrCodeRedeems, nil
}
