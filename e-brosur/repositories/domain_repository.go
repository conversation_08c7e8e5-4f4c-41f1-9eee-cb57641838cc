package repositories

import (
	"e-brochure/models"
	"errors"

	"gorm.io/gorm"
)

// domainRepository defines methods for domain data management
type DomainRepository interface {
	Create(domain *models.Domain) error
	GetByID(id uint) (*models.Domain, error)
	GetAllPaged(page, limit int) ([]models.Domain, error)
	GetAll() ([]models.Domain, error)
	Update(domain *models.Domain) error
	Delete(id uint) error
	Count() (int64, error)
}

// domainRepository implements domainRepository with a GORM database
type domainRepository struct {
	db *gorm.DB
}

// NewdomainRepository returns a new instance of domainRepository
func NewDomainRepository(db *gorm.DB) DomainRepository {
	return &domainRepository{db: db}
}

// Create inserts a new domain record
func (r *domainRepository) Create(domain *models.Domain) error {
	return r.db.Create(domain).Error
}

// GetByID retrieves a domain by its ID
func (r *domainRepository) GetByID(id uint) (*models.Domain, error) {
	var domain models.Domain
	if err := r.db.First(&domain, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &domain, nil
}

// GetAll retrieves all domains
func (r *domainRepository) GetAll() ([]models.Domain, error) {
	var domains []models.Domain
	if err := r.db.Find(&domains).Error; err != nil {
		return nil, err
	}
	return domains, nil
}

// Update modifies an existing domain record
func (r *domainRepository) Update(domain *models.Domain) error {
	return r.db.Save(domain).Error
}

// Delete removes a domain by its ID
func (r *domainRepository) Delete(id uint) error {
	return r.db.Delete(&models.Domain{}, id).Error
}

// GetAllPaged retrieves all domains with pagination
func (r *domainRepository) GetAllPaged(page, limit int) ([]models.Domain, error) {
	var domains []models.Domain
	offset := (page - 1) * limit
	if err := r.db.Offset(offset).Limit(limit).Find(&domains).Error; err != nil {
		return nil, err
	}
	return domains, nil
}

// Count returns the total number of 
func (r *domainRepository) Count() (int64, error) {
	var count int64
	if err := r.db.Model(&models.Domain{}).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}
