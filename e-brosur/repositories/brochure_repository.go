package repositories

import (
	"e-brochure/models"
	"errors"
	"fmt"

	"gorm.io/gorm"
)

// BrochureRepository defines methods for brochure data management
type BrochureRepository interface {
	Create(brochure *models.Brochure) error
	GetByID(id uint) (*models.Brochure, error)
	GetAllPaged(page, limit int, projectKey string) ([]models.Brochure, error)
	GetAll() ([]models.Brochure, error)
	Update(brochure *models.Brochure) error
	Delete(id uint) error
	Count() (int64, error)
	GetByTitlePaged(title string, page, limit int, projectKey string) ([]models.Brochure, error)
	CountByTitle(title string) (int64, error)
	AddBrochureToStore(store *models.Store, brochure *models.Brochure) error
	FindStoreByID(id uint) (*models.Store, error) // Returns store and error
	CreateStoreBrochure(storeBrochure *models.BrochureStore) error
	GetByRotator(rotatorURL uint) ([]models.Brochure, error)
	GetBrochureBodyByID(id uint) (*models.Brochure, error)
}

// brochureRepository implements BrochureRepository with a GORM database
type brochureRepository struct {
	db *gorm.DB
}

// NewBrochureRepository returns a new instance of brochureRepository
func NewBrochureRepository(db *gorm.DB) BrochureRepository {
	return &brochureRepository{db: db}
}

// Create inserts a new brochure record
func (r *brochureRepository) Create(brochure *models.Brochure) error {
	return r.db.Create(brochure).Error
}

// GetByID retrieves a brochure by its ID
func (r *brochureRepository) GetByID(id uint) (*models.Brochure, error) {
	var brochure models.Brochure
	if err := r.db.First(&brochure, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &brochure, nil
}

// GetAll retrieves all brochures
func (r *brochureRepository) GetAll() ([]models.Brochure, error) {
	var brochures []models.Brochure
	if err := r.db.Find(&brochures).Error; err != nil {
		return nil, err
	}
	return brochures, nil
}

// Update modifies an existing brochure record
func (r *brochureRepository) Update(brochure *models.Brochure) error {
	return r.db.Save(brochure).Error
}

// Delete removes a brochure by its ID
func (r *brochureRepository) Delete(id uint) error {
	return r.db.Delete(&models.Brochure{}, id).Error
}

// GetAllPaged retrieves all brochures with pagination
func (r *brochureRepository) GetAllPaged(page, limit int, projectKey string) ([]models.Brochure, error) {
	var brochures []models.Brochure
	offset := (page - 1) * limit
	if err := r.db.Where("project_key = ?", projectKey).Offset(offset).Limit(limit).Preload("Stores").Find(&brochures).Error; err != nil {
		return nil, err
	}
	return brochures, nil
}

// GetByTitlePaged retrieves all brochures with pagination by title
func (r *brochureRepository) GetByTitlePaged(title string, page, limit int, projectKey string) ([]models.Brochure, error) {
	var brochures []models.Brochure
	offset := (page - 1) * limit
	if err := r.db.Where("project_key = ?", projectKey).Where("title LIKE ?", fmt.Sprintf("%%%s%%", title)).Offset(offset).Limit(limit).Preload("Stores").Find(&brochures).Error; err != nil {
		return nil, err
	}
	return brochures, nil
}

// CountByTitle returns the total number of brochures with a given title
func (r *brochureRepository) CountByTitle(title string) (int64, error) {
	var count int64
	if err := r.db.Model(&models.Brochure{}).Where("title LIKE ?", fmt.Sprintf("%%%s%%", title)).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// Count returns the total number of brochures
func (r *brochureRepository) Count() (int64, error) {
	var count int64
	if err := r.db.Model(&models.Brochure{}).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// AddBrochureToStore associates a brochure with a store
func (r *brochureRepository) AddBrochureToStore(store *models.Store, brochure *models.Brochure) error {
    // This should correctly append the brochure to the store's many-to-many relationship
    return r.db.Model(store).Association("Brochures").Append(brochure)
}
func (r *brochureRepository) FindStoreByID(id uint) (*models.Store, error) {
	var store models.Store
	if err := r.db.First(&store, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, err // Return error if store is not found
		}
		return nil, err // Return any other error
	}
	return &store, nil
}
func (r *brochureRepository) CreateStoreBrochure(storeBrochure *models.BrochureStore) error {
	return r.db.Create(storeBrochure).Error
}

// GetByRotator retrieves a brochure using a rotator URL ID from the pivot table
func (r *brochureRepository) GetByRotator(rotatorURLID uint) ([]models.Brochure, error) {
	var brochures []models.Brochure
	if err := r.db.
		Joins("JOIN rotatorurl_brochures rb on brochures.id = rb.brochure_id").
		Where("rb.rotator_url_id = ?", rotatorURLID).
		Find(&brochures).
		Error; err != nil {
		return nil, err // Return any other error
	}
	return brochures, nil
}
// GetBrochureBodyByID retrieves a brochure body by ID
func (r *brochureRepository) GetBrochureBodyByID(id uint) (*models.Brochure, error) {
	var brochure models.Brochure
	if err := r.db.Select("body").First(&brochure, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, err // Return error if brochure is not found
		}
		return nil, err // Return any other error
	}
	return &brochure, nil
}

