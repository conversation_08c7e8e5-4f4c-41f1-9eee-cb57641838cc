package repositories

import (
	"e-brochure/models"
	"errors"
	"log"
	"math/rand"
	"strconv"

	"gorm.io/gorm"
)

// ProjectRepository defines methods for brochure data management
type ProjectRepository interface {
	Create(brochure *models.Project) error
	GetByID(id uint) (*models.Project, error)
	GetAllPaged(page, limit int, userID uint) ([]models.Project, error)
	GetAll() ([]models.Project, error)
	Update(brochure *models.Project) error
	Delete(id uint) error
	Count(userID uint) (int64, error)
	GetByNamePaged(name string, page, limit int, userID uint) ([]models.Project, error)
	CountByName(name string, userID uint) (int64, error)
}

// projectRepository implements ProjectRepository with a GORM database
type projectRepository struct {
	db *gorm.DB
}

// NewProjectRepository returns a new instance of ProjectRepository
func NewProjectRepository(db *gorm.DB) ProjectRepository {
	return &projectRepository{db: db}
}

// Create inserts a new brochure record
func (r *projectRepository) Create(project *models.Project) error {
	projectKey := generateProjectKey(r.db)
	project.ProjectKey = projectKey
	err := r.db.Create(project).Error
	if err != nil {
		return err
	}
	err = r.db.Exec(`CREATE SCHEMA IF NOT EXISTS "` + strconv.FormatUint(uint64(project.ID), 10) + `"`).Error
	if err != nil {
		return err
	}
	err = r.db.Exec(`CREATE TABLE IF NOT EXISTS "` + strconv.FormatUint(uint64(project.ID), 10) + `"."brochures" (LIKE public.brochures INCLUDING ALL)`).Error
	if err != nil {
		return err
	}
	err = r.db.Exec(`CREATE TABLE IF NOT EXISTS "` + strconv.FormatUint(uint64(project.ID), 10) + `"."stores" (LIKE public.stores INCLUDING ALL)`).Error
	if err != nil {
		return err
	}
	err = r.db.Exec(`CREATE TABLE IF NOT EXISTS "` + strconv.FormatUint(uint64(project.ID), 10) + `"."domains" (LIKE public.domains INCLUDING ALL)`).Error
	if err != nil {
		return err
	}
	err = r.db.Exec(`CREATE TABLE IF NOT EXISTS "` + strconv.FormatUint(uint64(project.ID), 10) + `"."rotator_urls" (LIKE public.rotator_urls INCLUDING ALL)`).Error
	if err != nil {
		return err
	}
	err = r.db.Exec(`CREATE TABLE IF NOT EXISTS "` + strconv.FormatUint(uint64(project.ID), 10) + `"."rotatorurl_brochures" (LIKE public.rotatorurl_brochures INCLUDING ALL)`).Error
	if err != nil {
		return err
	}
	err = r.db.Exec(`CREATE TABLE IF NOT EXISTS "` + strconv.FormatUint(uint64(project.ID), 10) + `"."settings" (LIKE public.settings INCLUDING ALL)`).Error
	if err != nil {
		return err
	}
	err = r.db.Exec(`CREATE TABLE IF NOT EXISTS "` + strconv.FormatUint(uint64(project.ID), 10) + `"."visitors" (LIKE public.visitors INCLUDING ALL)`).Error
	if err != nil {
		return err
	}
	return nil
}

func generateProjectKey(db *gorm.DB) string {
	var projectKey string
	for {
		projectKey = generateRandomString(6)
		var count int64
		if err := db.Model(&models.Project{}).Where("project_key = ?", projectKey).Count(&count).Error; err != nil {
			log.Fatal(err)
		}
		if count == 0 {
			break
		}
	}
	return projectKey
}
func generateRandomString(length int) string {
	const chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, length)
	for i := range result {
		result[i] = chars[rand.Intn(len(chars))]
	}
	return string(result)
}
// GetByID retrieves a brochure by its ID
func (r *projectRepository) GetByID(id uint) (*models.Project, error) {
	var brochure models.Project
	if err := r.db.First(&brochure, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &brochure, nil
}

// GetAll retrieves all brochures
func (r *projectRepository) GetAll() ([]models.Project, error) {
	var brochures []models.Project
	if err := r.db.Find(&brochures).Error; err != nil {
		return nil, err
	}
	return brochures, nil
}

// Update modifies an existing brochure record
func (r *projectRepository) Update(brochure *models.Project) error {
	return r.db.Save(brochure).Error
}

// Delete removes a brochure by its ID
func (r *projectRepository) Delete(id uint) error {
	return r.db.Delete(&models.Project{}, id).Error
}

// GetAllPaged retrieves all brochures with pagination
func (r *projectRepository) GetAllPaged(page, limit int, userID uint) ([]models.Project, error) {
	var brochures []models.Project
	offset := (page - 1) * limit
	if err := r.db.Where("user_id = ?", userID).Offset(offset).Limit(limit).Find(&brochures).Error; err != nil {
		return nil, err
	}
	return brochures, nil
}
// Count returns the total number of brochures
func (r *projectRepository) Count(userID uint) (int64, error) {
	var count int64
	if err := r.db.Where("user_id = ?", userID).Model(&models.Project{}).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}
// GetByNamePaged retrieves projects by name with pagination
func (r *projectRepository) GetByNamePaged(name string, page, limit int, userID uint) ([]models.Project, error) {
	var projects []models.Project
	offset := (page - 1) * limit
	if err := r.db.Where("name LIKE ? AND user_id = ?", "%"+name+"%", userID).Offset(offset).Limit(limit).Find(&projects).Error; err != nil {
		return nil, err
	}
	return projects, nil
}
// CountByName returns the total number of projects with a given name
func (r *projectRepository) CountByName(name string, userID uint) (int64, error) {
	var count int64
	if err := r.db.Model(&models.Project{}).Where("name LIKE ? AND user_id = ?", "%"+name+"%", userID).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}
