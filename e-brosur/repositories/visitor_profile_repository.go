package repositories

import (
	"e-brochure/models"
	"errors"

	"gorm.io/gorm"
)

// VisitorProfileRepository defines methods for visitor_profile data management
type VisitorProfileRepository interface {
	Create(*models.VisitorProfile) error
	FindByID(uint) (*models.VisitorProfile, error)
	GetByID(uint) (*models.VisitorProfile, error)
	FindByVisitorID(uint) ([]*models.VisitorProfile, error)
}

// visitorProfileRepository implements VisitorProfileRepository with a GORM database
type visitorProfileRepository struct {
	db *gorm.DB
}

// NewVisitorProfileRepository returns a new instance of visitorProfileRepository
func NewVisitorProfileRepository(db *gorm.DB) VisitorProfileRepository {
	return &visitorProfileRepository{db: db}
}

// Create creates a new visitor_profile
func (r *visitorProfileRepository) Create(vp *models.VisitorProfile) error {
	return r.db.Create(vp).Error
}

// FindByID retrieves a single visitor_profile by ID
func (r *visitorProfileRepository) FindByID(id uint) (*models.VisitorProfile, error) {
	var vp models.VisitorProfile
	if err := r.db.First(&vp, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &vp, nil
}

// GetByID retrieves a single visitor_profile by ID, without any error handling
func (r *visitorProfileRepository) GetByID(id uint) (*models.VisitorProfile, error) {
	var vp models.VisitorProfile
	r.db.First(&vp, id)
	return &vp, nil
}

// FindByVisitorID retrieves all visitor_profiles by visitor ID
func (r *visitorProfileRepository) FindByVisitorID(visitorID uint) ([]*models.VisitorProfile, error) {
	var vps []*models.VisitorProfile
	if err := r.db.Where("visitor_id = ?", visitorID).Find(&vps).Error; err != nil {
		return nil, err
	}
	return vps, nil
}

