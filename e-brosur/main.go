package main

import (
	"e-brochure/controllers"
	authController "e-brochure/controllers"
	"e-brochure/middlewares"
	setup "e-brochure/models"
	"e-brochure/repositories"
	"fmt"
	"image"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/chai2010/webp"
	"github.com/gin-gonic/gin"
	"github.com/nfnt/resize"
)

func main() {
	r := gin.Default()

	r.Use(corsMiddleware())
	r.GET("/token_sso", authController.TokenSso)
	log.Println("Server started on port 8009")
	db := setup.InitializeDB()
	storeRepo := repositories.NewStoreRepository(db)
	brochureRepo := repositories.NewBrochureRepository(db)
	affiliateRepo := repositories.NewAffiliateRepository(db)
	referralRepo := repositories.NewReferralRepository(db)
	rotatorUrlRepo := repositories.NewRotatorURLRepository(db)
	payloadRepo := repositories.NewPayloadRepository(db)
	visitorRepo := repositories.NewVisitorRepository(db)
	visitorProfileRepo := repositories.NewVisitorProfileRepository(db)
	settingRepo := repositories.NewSettingRepository(db)
	projectRepo := repositories.NewProjectRepository(db)
	domainRepo := repositories.NewDomainRepository(db)
	userRepo := repositories.NewUserRepository(db)
	provinceRepo := repositories.NewProvinceRepository(db)
	regencyRepo := repositories.NewRegencyRepository(db)
	qrCodeRepo := repositories.NewQrCodeRepository(db)
	qrCodeRedeemRepo := repositories.NewQRCodeRedeemRepository(db)
	connectorRepository := repositories.NewConnectorRepository(db)
	campaignRepository := repositories.NewCampaignRepository(db)
	budgetRepository := repositories.NewBudgetRepository(db)
	influencerRepository := repositories.NewInfluencerRepository(db)
	scheduleRepository := repositories.NewScheduleRepository(db)
	formFillRepository := repositories.NewFormFillRepository(db)

	storeController := controllers.NewStoreController(storeRepo, userRepo)
	brochureController := controllers.NewBrochureController(brochureRepo, rotatorUrlRepo, visitorProfileRepo, payloadRepo, storeRepo, userRepo, visitorRepo, qrCodeRepo)
	affiliateController := controllers.NewAffiliateController(affiliateRepo)
	referralController := controllers.NewReferralController(referralRepo)
	rotatorUrlController := controllers.NewRotatorURLController(rotatorUrlRepo, userRepo)
	visitorController := controllers.NewVisitorController(visitorRepo)
	settingsController := controllers.NewSettingsController(settingRepo, userRepo, storeRepo, qrCodeRepo)
	projectController := controllers.NewProjectController(projectRepo, userRepo)
	domainController := controllers.NewDomainController(domainRepo)
	provinceController := controllers.NewProvinceController(provinceRepo)
	regencyController := controllers.NewRegencyController(regencyRepo)
	qrCodeController := controllers.NewQRCodeController(qrCodeRepo)
	qrCodeRedeemController := controllers.NewQRCodeRedeemController(qrCodeRedeemRepo, qrCodeRepo, visitorProfileRepo, userRepo)
	connectorController := controllers.NewConnectorController(connectorRepository, userRepo)
	campaignController := controllers.NewCampaignController(campaignRepository, influencerRepository)
	budgetController := controllers.NewBudgetController(budgetRepository)
	influencerController := controllers.NewInfluencerController(influencerRepository)
	scheduleController := controllers.NewScheduleController(scheduleRepository)
	payloadController := controllers.NewPayloadController(payloadRepo)
	formFillController := controllers.NewFormFillController(formFillRepository, visitorRepo, qrCodeRepo, storeRepo)
	// route  return test

	protected := r.Group("/api/v1")
	protected.POST("update-data", brochureController.UpdateData)
	// r.GET("single-page/:url", brochureController.RenderJs)
	r.GET("p/:id", brochureController.PreviewBrochure)
	//r.get respose test
	r.GET("/test", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "Hello World"})
	})

	r.GET("b/:url", brochureController.RenderBrochure)
	r.GET("second-page", brochureController.CountAndConvertVisitorId)
	r.POST("/register", authController.RegisterUsingPhoneAndPassword)
	r.POST("/login", authController.LoginUsingPhoneAndPassword)
	r.POST("/receive-form-qr", formFillController.Create)
	r.POST("/receive-whatsapp-qr", formFillController.CreateFormWhatsapp)
	r.GET("/store-loc/:id", formFillController.ShowStoreLoc)
	r.GET("/get-order-store", formFillController.GetOrderStore)
	r.POST("/upload-image", func(c *gin.Context) {
		// Retrieve the uploaded file
		file, _, err := c.Request.FormFile("image")
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"code": 400, "file": "", "message": "Failed to get the file", "size": 0})
			return
		}
		defer file.Close()

		// Decode the uploaded image (assuming JPEG input)
		img, _, err := image.Decode(file)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"code": 400, "file": "", "message": "Failed to decode the image", "size": 0})
			return
		}

		// Resize the image to 3 sizes
		sizes := []struct {
			width uint
			name  string
		}{
			{width: 300, name: "small"},
			{width: 600, name: "medium"},
			{width: 1024, name: "large"},
		}

		var results []gin.H
		time := time.Now().UnixNano()
		for _, size := range sizes {
			// Resize the image
			resizedImg := resize.Resize(size.width, 0, img, resize.Lanczos3)

			// Create the output file
			outputPath := fmt.Sprintf("./qr_codes/%s_%d.webp", size.name, time)
			outFile, err := os.Create(outputPath)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "file": "", "message": "Failed to create output file", "size": 0})
				return
			}
			defer outFile.Close()

			// Encode the image to WebP format
			err = webp.Encode(outFile, resizedImg, &webp.Options{Quality: 80})
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "file": "", "message": "Failed to encode image to WebP", "size": 0})
				return
			}

			results = append(results, gin.H{"code": 200, "image_id": strconv.FormatInt(time, 10), "message": "Image processed successfully", "size": size.width})
		}

		c.JSON(http.StatusOK, gin.H{"code": 200, "image_id": strconv.FormatInt(time, 10), "message": "Image processed successfully", "size": sizes[0].width})
	})
	// Handle missing images
	r.GET("/qr-codes/:filename", func(c *gin.Context) {
		filename := c.Param("filename")
		filePath := "./qr-codes/" + filename

		// Check if the file exists
		if _, err := os.Stat(filePath); err != nil {
			// If not found, return the placeholder image
			c.Data(http.StatusNotFound, "image/png", []byte{})
		}

		// Serve the requested file if it exists
		c.File(filePath)
	})
	//test
	r.GET("/qr_codes/:filename", func(c *gin.Context) {
		filename := c.Param("filename")
		filePath := "./qr_codes/" + filename

		// Check if the file exists
		if _, err := os.Stat(filePath); err != nil {
			// If not found, return the placeholder image
			c.Data(http.StatusNotFound, "image/jpg", []byte{})
		}

		// Serve the requested file if it exists
		c.File(filePath)
	})
	r.GET("/files/:filename", func(c *gin.Context) {
		filename := c.Param("filename")
		filePath := "./files/" + filename

		// Check if the file exists
		if _, err := os.Stat(filePath); err != nil {
			// If not found, return the placeholder image
			c.Data(http.StatusNotFound, "image/png", []byte{})
		}

		// Serve the requested file if it exists
		c.File(filePath)
	})
	protected.Use(middlewares.AuthMiddleware()) // Apply AuthMiddleware to all routes in this group
	{
		storeRoutes := protected.Group("/stores")
		{
			storeRoutes.POST("", storeController.CreateStore)
			storeRoutes.GET("", storeController.GetStores)
			storeRoutes.GET("/:id", storeController.GetStore)
			storeRoutes.PUT("/:id", storeController.UpdateStore)
			storeRoutes.DELETE("/:id", storeController.DeleteStore)
		}
		brochureRoutes := protected.Group("/brochures")
		{
			brochureRoutes.POST("", brochureController.CreateBrochure)
			brochureRoutes.GET("", brochureController.GetBrochures)
			brochureRoutes.GET("/:id", brochureController.GetBrochure)
			brochureRoutes.PUT("/:id", brochureController.UpdateBrochure)
			brochureRoutes.DELETE("/:id", brochureController.DeleteBrochure)
		}
		//referral
		referralRoutes := protected.Group("/referrals")
		{
			referralRoutes.POST("", referralController.CreateReferral)
			referralRoutes.GET("", referralController.GetReferrals)
			referralRoutes.GET("/:id", referralController.GetReferralByID)
			referralRoutes.DELETE("/:id", referralController.DeleteReferral)
		}

		//affiliate

		affiliateRoutes := protected.Group("/affiliates")
		{
			affiliateRoutes.POST("", affiliateController.CreateAffiliate)
			affiliateRoutes.GET("", affiliateController.GetAffiliates)
			affiliateRoutes.GET("/:id", affiliateController.GetAffiliate)
			affiliateRoutes.PUT("/:id", affiliateController.UpdateAffiliate)
			affiliateRoutes.DELETE("/:id", affiliateController.DeleteAffiliate)
		}

		rotatorRoutes := protected.Group("/rotatorurls")
		{
			rotatorRoutes.POST("", rotatorUrlController.CreateRotatorURL)
			rotatorRoutes.GET("", rotatorUrlController.GetRotatorURLs)
			rotatorRoutes.GET("/:id", rotatorUrlController.GetRotatorURL)
			rotatorRoutes.PUT("/:id", rotatorUrlController.UpdateRotatorURL)
			rotatorRoutes.DELETE("/:id", rotatorUrlController.DeleteRotatorURL)
		}
		visitorRoutes := protected.Group("/visitor")
		{
			visitorRoutes.GET("", visitorController.GetAllVisitors)
			visitorRoutes.GET("/:id", visitorController.GetVisitorByID)
			visitorRoutes.POST("", visitorController.CreateVisitor)
			visitorRoutes.PUT("/:id", visitorController.UpdateVisitor) // Update route
			visitorRoutes.DELETE("/:id", visitorController.DeleteVisitor)
		}
		settingRoutes := protected.Group("/setting")
		{
			settingRoutes.GET("", settingsController.GetAllSettings)
			settingRoutes.GET("/:id", settingsController.GetSettingsByID)
			settingRoutes.POST("", settingsController.CreateSettings)
			settingRoutes.PUT("/:id", settingsController.UpdateSettings) // Update route
			settingRoutes.DELETE("/:id", settingsController.DeleteSettings)
			settingRoutes.GET("/whatsapp", settingsController.GetSettingsByProjectKey)
			settingRoutes.PUT("/whatsapp", settingsController.UpdateSettingsByProjectKey)
		}
		projectRoutes := protected.Group("/projects")
		{
			projectRoutes.GET("", projectController.GetAllProjects)
			projectRoutes.GET("/:id", projectController.GetProjectsByID)
			projectRoutes.POST("", projectController.CreateProjects)
			projectRoutes.PUT("/:id", projectController.UpdateProjects) // Update route
			projectRoutes.DELETE("/:id", projectController.DeleteProjects)
		}
		domainRoutes := protected.Group("/domains")
		{
			domainRoutes.GET("", domainController.GetAllDomains)
			domainRoutes.GET("/:id", domainController.GetDomainsByID)
			domainRoutes.POST("", domainController.CreateDomains)
			domainRoutes.PUT("/:id", domainController.UpdateDomains) // Update route
			domainRoutes.DELETE("/:id", domainController.DeleteDomains)
		}
		provinceRoutes := protected.Group("/provinces")
		{
			provinceRoutes.GET("", provinceController.GetAllProvinces)
			provinceRoutes.GET("/:id", provinceController.GetProvinceByID)
			provinceRoutes.POST("", provinceController.CreateProvince)
			provinceRoutes.PUT("/:id", provinceController.UpdateProvince) // Update route
			provinceRoutes.DELETE("/:id", provinceController.DeleteProvince)
		}
		regencyRoutes := protected.Group("/regencies")
		{
			regencyRoutes.GET("", regencyController.GetAllRegencies)
			regencyRoutes.GET("/:id", regencyController.GetRegencyByID)
			regencyRoutes.POST("", regencyController.CreateRegency)
			regencyRoutes.PUT("/:id", regencyController.UpdateRegency) // Update route
			regencyRoutes.DELETE("/:id", regencyController.DeleteRegency)
		}
		qrcodeRoutes := protected.Group("/qr_codes")
		{
			qrcodeRoutes.GET("", qrCodeController.GetAllQRCodes)
			qrcodeRoutes.GET("/:id", qrCodeController.GetQRCodeByID)
			qrcodeRoutes.POST("", qrCodeController.CreateQRCode)
			qrcodeRoutes.PUT("/:id", qrCodeController.UpdateQRCode) // Update route
			qrcodeRoutes.DELETE("/:id", qrCodeController.DeleteQRCode)
		}
		qrCodeRedeemRoutes := protected.Group("/qr_code_redeems")
		{
			qrCodeRedeemRoutes.GET("", qrCodeRedeemController.GetAllQRCodeRedeems)
			qrCodeRedeemRoutes.GET("/:id", qrCodeRedeemController.GetQRCodeRedeemByID)
			qrCodeRedeemRoutes.POST("", qrCodeRedeemController.CreateQRCodeRedeem)
			qrCodeRedeemRoutes.PUT("/:id", qrCodeRedeemController.UpdateQRCodeRedeem) // Update route
			qrCodeRedeemRoutes.DELETE("/:id", qrCodeRedeemController.DeleteQRCodeRedeem)
		}
		connectorRoutes := protected.Group("/connectors")
		{
			connectorRoutes.GET("", connectorController.GetConnectors)
			connectorRoutes.GET("/:id", connectorController.GetConnector)
			connectorRoutes.POST("", connectorController.CreateConnector)
			connectorRoutes.PUT("/:id", connectorController.UpdateConnector) // Update route
			connectorRoutes.DELETE("/:id", connectorController.DeleteConnector)
			connectorRoutes.GET("/list_static_dropdown", connectorController.ListStaticDropdown)
		}
		campaignRoutes := protected.Group("/campaigns")
		{
			campaignRoutes.GET("", campaignController.GetAllCampaigns)
			campaignRoutes.GET("/:id", campaignController.GetCampaign)
			campaignRoutes.POST("", campaignController.CreateCampaign)
			campaignRoutes.POST("/assign-influencer", campaignController.AssignInfluencer)
			campaignRoutes.PUT("/:id", campaignController.UpdateCampaign) // Update route
			campaignRoutes.DELETE("/:id", campaignController.DeleteCampaign)
		}
		budgetRoutes := protected.Group("/budgets")
		{
			budgetRoutes.GET("", budgetController.GetBudgets)
			budgetRoutes.GET("/:id", budgetController.GetBudget)
			budgetRoutes.POST("", budgetController.CreateBudget)
			budgetRoutes.PUT("/:id", budgetController.UpdateBudget) // Update route
			budgetRoutes.DELETE("/:id", budgetController.DeleteBudget)
		}
		influencerRoutes := protected.Group("/influencers")
		{
			influencerRoutes.GET("", influencerController.GetAllInfluencers)
			influencerRoutes.GET("/:id", influencerController.GetInfluencerByID)
			influencerRoutes.POST("", influencerController.CreateInfluencer)
			influencerRoutes.PUT("/:id", influencerController.UpdateInfluencer) // Update route
			influencerRoutes.DELETE("/:id", influencerController.DeleteInfluencer)
		}
		scheduleRoutes := protected.Group("/schedules")
		{
			scheduleRoutes.GET("", scheduleController.GetAllSchedules)
			scheduleRoutes.GET("/:id", scheduleController.GetScheduleByID)
			scheduleRoutes.POST("", scheduleController.CreateSchedule)
			scheduleRoutes.PUT("/:id", scheduleController.UpdateSchedule) // Update route
			scheduleRoutes.DELETE("/:id", scheduleController.DeleteSchedule)
		}

		protected.GET("/data", func(c *gin.Context) {
			username := c.GetString("username")
			c.JSON(http.StatusOK, gin.H{"message": "Protected data accessed", "user": username})
		})

		protected.POST("/user-wa-config", settingsController.AddWaConfig)
		protected.PUT("/user", settingsController.UpdateProjectKey)
		protected.GET("/user", settingsController.GetUserData)
		protected.GET("/whatsapp-number", settingsController.GetWhatsappNumber)
		protected.PUT("/user-store", settingsController.UpdateStoreUser)
		protected.GET("/profile", func(c *gin.Context) {
			username := c.GetString("username")
			currentUser := c.GetString("userKey")
			c.JSON(http.StatusOK, gin.H{"message": "Profile data", "user": username, "user_id": currentUser})
		})
		protected.POST("/scan_qr", qrCodeRedeemController.RedeemQRCodeByCode)
		r.GET("/sendData", brochureController.SendData)
		r.POST("/payload", payloadController.CreatePayload)
		r.POST("/payload-whatsapp", settingsController.WhatsappPayload)
	}

	r.Run(":8009")

	setup.InitializeDB()
}
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}
