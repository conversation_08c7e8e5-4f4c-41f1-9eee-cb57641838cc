package models

import "time"

// Influencer represents the influencer model
type Influencer struct {
    ID                uint   `gorm:"primaryKey" json:"id"`
    Name              string `gorm:"size:255;not null" json:"name"`
    BankAccount       string `gorm:"size:255;not null" json:"bank_account"`
    AccountNumber     string `gorm:"size:255;not null" json:"account_number"`
    Phone             string `gorm:"size:255" json:"phone"`
    Address           string `gorm:"size:255" json:"address"`
    PicName           string `gorm:"size:255" json:"pic_name"`
    Instagram         string `gorm:"size:255" json:"instagram"`
    TikTok            string `gorm:"size:255" json:"tiktok"`
    SlotContent       string `gorm:"type:text" json:"slot_content"`
    CreatedAt         time.Time      `json:"created_at" gorm:"autoCreateTime"`
    AverageRateCard   *float64       `json:"average_rate_card,omitempty" gorm:"type:float;null"`
    RateCard          float64        `json:"rate_card"`
    UpdatedAt         time.Time      `json:"updated_at" gorm:"autoUpdateTime"`
}
