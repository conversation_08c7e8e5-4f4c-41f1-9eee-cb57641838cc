package models

import "time"

// Province represents the province model
type Province struct {
	ID        uint      `gorm:"primaryKey" json:"id"`
	Name      string    `gorm:"size:255;not null" json:"name"`
	AltName   string    `gorm:"size:255" json:"alt_name"`
	Latitude  string    `gorm:"size:50" json:"latitude"`
	Longitude string    `gorm:"size:50" json:"longitude"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}