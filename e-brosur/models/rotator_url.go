package models

import "time"

// RotatorUR<PERSON> represents the AI Optimizer model
type Rotator<PERSON><PERSON> struct {
	ID         uint       `gorm:"primaryKey" json:"id"`
	Name       string     `gorm:"size:255" json:"name"`
	Impression int        `json:"impression"`
	Click      int        `json:"click"`
	Conversion int        `json:"conversion"` // Count of successful conversions
	FormFilled  int        `json:"form_filled"` // Count of form fills
	Redeem      int        `json:"redeem"` // Count of successful redemptions
	Spent       uint       `json:"spent"` // Total amount spent on campaign
	CostPerRedeem *uint    `json:"cost_per_redeem,omitempty"` // Cost per redemption, calculated as Spent / Redeem
	ShortURL   string     `gorm:"size:255" json:"short_url"`
	Brochures  []*Brochure `gorm:"many2many:rotatorurl_brochures;" json:"brochures,omitempty"`
	ProjectKey string    `json:"project_key"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}