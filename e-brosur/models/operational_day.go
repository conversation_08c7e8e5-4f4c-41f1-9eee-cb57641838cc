package models

import (
	"time"

	"gorm.io/gorm"
)

type OperationalDay struct {
	ID         uint `gorm:"primaryKey" json:"id"`
	Day        string         `gorm:"not null" json:"day"`
	OpenHours  string         `gorm:"not null" json:"open_hours"`
	CloseHours string         `gorm:"not null" json:"close_hours"`
	CreatedAt  time.Time      `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt  time.Time      `gorm:"autoUpdateTime" json:"updated_at"`
	DeletedAt  gorm.DeletedAt `gorm:"index" json:"deleted_at"`
    StoreID   uint   `gorm:"index"`
}
