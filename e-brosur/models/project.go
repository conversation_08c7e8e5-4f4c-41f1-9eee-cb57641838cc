package models

import "time"

// Project represents the Project model
type Project struct {
	ID        		uint   `gorm:"primaryKey" json:"id"`
	Name     		string `gorm:"size:255;not null" json:"name"`
	ProjectKey     	string `gorm:"size:255;not null" json:"project_key"`
	Status    		string `gorm:"size:30" json:"status"`
	UserID     		uint   `gorm:"index" json:"user_id"`
	CreatedAt 		time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt 		time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}