package models

import "time"

type Campaign struct {
	ID        uint   `gorm:"primaryKey" json:"id"`
	CampaignName string    `gorm:"type:varchar(255);not null" json:"campaign_name"`
	Influencers  []Influencer `gorm:"many2many:campaign_influencers;" json:"influencers,omitempty"`
	StartDate    time.Time `gorm:"not null" json:"start_date"`
	EndDate      time.Time `gorm:"not null" json:"end_date"`
	Description  string    `gorm:"type:text" json:"description"`
	BudgetID     uint      `gorm:"not null" json:"budget_id"`
	Spending     *uint      `gorm:"null" json:"spending,omitempty"`
	Cpm          *uint      `gorm:"null" json:"cpm,omitempty"`
}
