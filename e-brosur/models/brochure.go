package models

import "time"

// Brochure represents the brochure model
type Brochure struct {
	ID        uint   `gorm:"primaryKey" json:"id"`
	Title     string `gorm:"size:255;not null" json:"title"`
	Body      string `gorm:"type:text" json:"body"`
	Thumbnail string `gorm:"text" json:"thumbnail"`
	ProjectKey string    `gorm:"size:255" json:"project_key"`
	StoreID    uint   `json:"store_id"`
	Stores    []Store `gorm:"many2many:brochure_stores;"`
	RotatorURLs []RotatorURL `gorm:"many2many:rotatorurl_brochures;"`
	Status    string `gorm:"size:30" json:"status"`
	Alpha     uint   `json:"alpha"`
	Beta      uint   `json:"beta"`
	Impressions uint   `json:"impressions"`
	Clicks      uint   `json:"clicks"` // number of clicks on the brochure
	FormFilled  uint   `json:"form_filled"` // number of form filled
	Redeem      uint   `json:"redeem"` // number of redeems
	Spent       uint   `json:"spent"` // total amount spent on facebook ads
	CostPerRedeem *uint `json:"cost_per_redeem,omitempty"` // cost per redeem, calculated as spent / redeem
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}