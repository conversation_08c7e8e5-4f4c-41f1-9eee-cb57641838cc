package models

import (
	"log"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

//	func ConnectDatabase() {
//		db, err := sql.Open("postgres", os.Getenv("DATABASE_URL"))
//		if err != nil {
//			log.Fatal(err)
//		}else{
//			log.Println("Database connected")
//		}
//		defer db.Close()
//	}
func InitializeDB() *gorm.DB {
	dsn := "host=db user=postgres password=postgres-e-brochure dbname=e-brochure sslmode=disable"
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	db.AutoMigrate(&User{})
	db.AutoMigrate(&Store{})
	db.AutoMigrate(&Brochure{})
	db.AutoMigrate(&RotatorURL{})
	db.AutoMigrate(&Project{})
	db.AutoMigrate(&Domain{})
	db.AutoMigrate(&Visitor{})
	db.AutoMigrate(&Setting{})
	db.AutoMigrate(&VisitorProfile{})
	db.AutoMigrate(&Payload{})
	db.AutoMigrate(&Province{})
	db.AutoMigrate(&Regency{})
	db.AutoMigrate(&StoreOperationalDay{})
	db.AutoMigrate(&BrochureStore{})
	db.AutoMigrate(&OperationalDay{})
	db.AutoMigrate(&QRCode{})
	db.AutoMigrate(&QRCodeRedeem{})
	db.AutoMigrate(&Connector{})
	db.AutoMigrate(&Campaign{})
	db.AutoMigrate(&Budget{})
	db.AutoMigrate(&Influencer{})
	db.AutoMigrate(&Schedule{})
	db.AutoMigrate(&ConnectorLog{})
	db.AutoMigrate(&FormFill{})
	db.AutoMigrate(&Affiliate{})
	db.AutoMigrate(&Referral{})
	db.AutoMigrate(&Refferal{})

	if err != nil {
		log.Fatalf("Failed to connect to the database: %v", err)
	}
	return db
}
