package models

import "time"

type User struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	Token     string    `json:"token"`
	UserKey   string    `json:"user_key"`
	Name      string    `json:"name"`
	Phone     string    `json:"phone"`
	Email     string    `json:"email"`
	CsName      *string    `json:"cs_name,omitempty"`
	CsPhone     *string    `json:"cs_phone,omitempty"`
	CsWebhook   *string    `json:"cs_webhook,omitempty"`
	StoreID   *uint     `json:"store_id,omitempty" gorm:"index"`
	ProjectKey string    `json:"project_key"`
	ParentID   *uint     `json:"parent_id" gorm:"default:null"`
	CreatedAt time.Time `json:"created_at"`
	Password  []byte    `json:"password" gorm:"type:text"`
	UpdatedAt time.Time `json:"updated_at"`
}