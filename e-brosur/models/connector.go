package models

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

// Connector represents the connector model
type Connector struct {
	ID                         uint           `json:"id" gorm:"primaryKey"`
	Name                       string         `json:"name"`
	Token                      string         `json:"token"`
	PixelID                    string         `json:"pixel_id"`
	Type                       string         `json:"type"`
	CreatedAt                  time.Time      `json:"created_at"`
	UpdatedAt                  time.Time      `json:"updated_at"`
	BrochureVisitLP            string           `json:"brochure_visit_lp"`
	ProjectKey                 string           `json:"project_key"`
	BrochureClickCTA           string           `json:"brochure_click_cta"`
	BrochureWhatsAppSendMessage string          `json:"brochure_whatsapp_send_message"`
	BrochureFillForm           string           `json:"brochure_fill_form"`
}

// BeforeCreate hook to ensure the type is valid
func (c *Connector) BeforeCreate(tx *gorm.DB) (err error) {
	validTypes := map[string]bool{
		"Facebook":   true,
		"Tiktok":     true,
		"Snack Video": true,
		"MGID":       true,
	}
	if !validTypes[c.Type] {
		return fmt.Errorf("invalid type: %s", c.Type)
	}
	return
}

