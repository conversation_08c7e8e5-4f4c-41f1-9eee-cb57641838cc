package models

import (
	"encoding/json"
	"fmt"
	"time"
)
type StringOrFloat string

func (sf *StringOrFloat) UnmarshalJSON(data []byte) error {
    var f float64
    if err := json.Unmarshal(data, &f); err == nil {
        *sf = StringOrFloat(fmt.Sprintf("%f", f))
        return nil
    }
    var s string
    if err := json.Unmarshal(data, &s); err != nil {
        return err
    }
    *sf = StringOrFloat(s)
    return nil
}
// Store represents the store model
type Store struct {
	ID            uint   `gorm:"primaryKey" json:"id"`
	Name          string `gorm:"size:255;not null" json:"name"`
	Location      string `gorm:"size:255" json:"location"`
	Latitude      string  `gorm:"not null" json:"latitude"`
	Longitude     string  `gorm:"not null" json:"longitude"`
	ContactNumber string `gorm:"size:50" json:"contact_number"`
	UserID         uint   `gorm:"index" json:"user_id"`
	Email         string `gorm:"size:100" json:"email"`
	Status        string `gorm:"size:30" json:"status"`
	ProjectKey 	  string    `json:"project_key"`
	Instagram     string `gorm:"size:255" json:"instagram"`
	Facebook      string `gorm:"size:255" json:"facebook"`
	CreatedAt 	time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt 	time.Time `json:"updated_at" gorm:"autoUpdateTime"`
	OperationalDays []OperationalDay `json:"operational_days"`
	RegencyID      uint   `gorm:"index" json:"regency_id"`
	Address         string `gorm:"size:255" json:"address"`
    Brochures []Brochure `gorm:"many2many:store_brochures;"`
}
