package models

import (
	"time"
)

type Affiliate struct {
	ID           uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	Code         string    `gorm:"size:255;not null" json:"code"`
	Commission   float64   `gorm:"not null" json:"commission"`
	AffiliateURL string    `gorm:"size:255" json:"affiliate_url"`
	CreatedAt    time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt    time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}
