package models

import (
	"time"
)

type Visitor struct {
    ID           uint           `json:"id" gorm:"primaryKey"`
    VisitorProfiles []*VisitorProfile `gorm:"many2many:visitor_visitorprofiles;" json:"visitor_profiles,omitempty"`
    VisitorID   string           `json:"visitor_id"`
    Date         time.Time      `json:"date"`
    Hour         time.Time      `json:"hour"`
    PhoneNumber  *string        `json:"phone_number,omitempty"` // Only if WhatsApp conversion
    FormFill     *string        `json:"form_fill,omitempty"`    // JSON Array of form data
    BrochureID   uint           `json:"brochure_id"`
    Brochure     Brochure       `json:"brochure"`
    AffiliateID  uint           `json:"affiliate_id"`
    RotatorURLID uint           `json:"rotator_url_id"`
    QRCode       string         `json:"qr_code,omitempty"`
    Fbclid       *string        `json:"fbclid,omitempty"`
    Fbp          *string        `json:"fbp,omitempty"`
    Fbc          *string        `json:"fbc,omitempty"`
    Ttclid       *string        `json:"ttclid,omitempty"`
    Gclid        *string        `json:"gclid,omitempty"`
    Wbraid       *string        `json:"wbraid,omitempty"`
    Gbraid       *string        `json:"gbraid,omitempty"`
    Gs           *string        `json:"gs,omitempty"`
    ClickID      *string        `json:"click_id,omitempty"`
    ProjectKey   string         `json:"project_key"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}