package models

import (
	"time"
)

type Refferal struct {
	ID               uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	Name             string    `gorm:"size:255;not null" json:"name"`
	Phone            string    `gorm:"size:15;not null" json:"phone"`
	TotalTransaction float64   `gorm:"not null" json:"total_transaction"`
	TotalCommission  float64   `gorm:"not null" json:"total_commission"`
	AffiliateID      uint      `gorm:"not null" json:"affiliate_id"`            // Foreign key to Affiliate
	Affiliate        Affiliate `gorm:"foreignKey:AffiliateID" json:"affiliate"` // Associating with the Affiliate model
	CreatedAt        time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt        time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}
