package models

import (
	"time"
)

// QRCode represents the QR code model
type QRCode struct {
	ID        uint      `gorm:"primaryKey" json:"id"`
	CodeFile string `gorm:"type:text" json:"code_file"`
	Code      string    `gorm:"not null" json:"code"`
	Caption   string    `gorm:"type:text" json:"caption"`
	DefaultMessage string `gorm:"type:text" json:"default_message"`
	BrochureID uint      `gorm:"index" json:"brochure_id"`
	MergeFile  string    `gorm:"type:text" json:"merge_file"`
	VisitorProfileID uint      `gorm:"index" json:"visitor_profile_id"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}
