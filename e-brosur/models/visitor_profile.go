package models

import (
	"time"
)

type VisitorProfile struct {
    ID           uint           `json:"id" gorm:"primaryKey"`
    IP           string         `json:"ip"`
    BrowserAgent string         `json:"browser_agent"`
    Referral     string         `json:"referral"`  // JSON Array of form data
    VisitorID    string        `json:"visitor_id,omitempty"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}