package models

import "time"

// QRCodeRedeem represents the qr_code_redeem model
type QRCodeRedeem struct {
	ID           uint      `gorm:"primaryKey" json:"id"`
	QRCodeID     uint      `gorm:"index" json:"qr_code_id"`
	QRCode       QRCode    `gorm:"foreignKey:QRCodeID" json:"qr_code"`
	VisitorID    uint      `gorm:"index" json:"visitor_id"`
	VisitorProfile VisitorProfile `gorm:"foreignKey:id;references:VisitorID" json:"visitor_profile"`
	IP           string    `gorm:"type:inet" json:"ip"`
	Phone        string    `json:"phone"`
	Datetime     time.Time `json:"datetime"`
	Amount       uint      `json:"amount"`
	BrowserAgent string    `json:"browser_agent"`
	StoreID      uint      `gorm:"index" json:"store_id"`
	Referral     string    `json:"referral"`
	CreatedAt    time.Time `json:"created_at" gorm:"autoCreateTime"`
}


