package models

import "time"

// Domain represents the domain model
type Domain struct {
	ID        uint   `json:"id" gorm:"primaryKey"`
	Name      string `json:"name" gorm:"type:varchar(255);not null;unique"`
	ProjectKey string `json:"project_key" gorm:"type:varchar(255);not null;index"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}
