package models

import "time"

type Setting struct {
	ID              uint       `json:"id" gorm:"primaryKey"`
	WhatsAppAccount *string    `json:"whatsapp_account,omitempty"`
	BusinessProfile *string    `json:"business_profile,omitempty" gorm:"type:json"`
	AccountInfo     *string    `json:"account_info,omitempty" gorm:"type:json"`
	MQL             *int       `json:"mql,omitempty"`
	FormatID        *string    `json:"format_id,omitempty" gorm:"type:varchar(255);null"`
	CreatedAt       time.Time  `json:"created_at" gorm:"autoCreateTime"`
	ProjectKey      string     `json:"project_key" gorm:"type:varchar(255);not null;unique"`
	UpdatedAt       time.Time  `json:"updated_at" gorm:"autoUpdateTime"`
}
