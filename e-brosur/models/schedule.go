package models

import "time"

// Schedule represents the schedule model
type Schedule struct {
	ID          uint   `gorm:"primaryKey" json:"id"`
	InfluencerID uint `gorm:"index" json:"influencer_id"`
	CampaignID   uint `gorm:"index" json:"campaign_id"`
	Task      string    `gorm:"size:255;not null" json:"task"`
	Date      time.Time `json:"date"`
	PlacementType string    `gorm:"size:100" json:"placement_type"`
	Platform   string `gorm:"size:100" json:"platform"`
	RateCard  float64 `json:"rate_card"`

}
