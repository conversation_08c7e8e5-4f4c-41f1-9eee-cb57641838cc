package helper

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt"
	"golang.org/x/crypto/bcrypt"
)
var jwtSecret = []byte("your_secret_key")
func ResponseJSON(c *gin.Context, code int, payload interface{}) {
	// Respond with the status code and payload in JSON format
	c.JSON(code, payload)
}
func GenerateToken(username string, userKey string) (string, error) {
	claims := jwt.MapClaims{
		"username": username,
		"userKey":  userKey,
		"exp":      time.Now().AddDate(0, 6, 0).Unix(), // Token expires in 6 months
	}

	// Create a new token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Sign the token with the secret key
	signedToken, err := token.SignedString(jwtSecret)
	if err != nil {
		return "", fmt.Errorf("failed to generate token: %w", err)
	}

	return signedToken, nil
}
func CheckPasswordHash(password, hash string) error {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	if err != nil {
		return fmt.Errorf("password does not match: %w", err)
	}
	return nil
}

