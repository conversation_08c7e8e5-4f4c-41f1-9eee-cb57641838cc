package main

import (
	"fmt"
	"math/rand"
	"time"

	"gorm.io/gorm"
)

// Define the model
type Product struct {
	ID    uint   `json:"id" gorm:"primaryKey"`
	Name  string `json:"name"`
	Price uint   `json:"price"`
}

var db *gorm.DB
var err error

type LandingPage struct {
	ID         int
	Impressions int
	Clicks      int
}

// Initialize landing pages with specific probabilities
var pages = []*LandingPage{
	{ID: 1, Impressions: 0, Clicks: 0},
	{ID: 2, Impressions: 0, Clicks: 0},
	{ID: 3, Impressions: 0, Clicks: 0},
}

// Define the probabilities for each landing page
var probabilities = []float64{0.4, 0.4, 0.2}

// SelectLandingPage chooses a landing page based on predefined probabilities
func SelectLandingPage() *LandingPage {
	r := rand.Float64()
	cumulativeProbability := 0.0

	for i, prob := range probabilities {
		cumulativeProbability += prob
		if r < cumulativeProbability {
			return pages[i]
		}
	}

	// Fallback to the first page (just in case)
	return pages[0]
}

// Simulate button click, register the event
func ButtonClick(page *LandingPage) {
	page.Clicks++
}

// ShowPage simulates showing the page and tracks impressions
func ShowPage() *LandingPage {
	page := SelectLandingPage()
	page.Impressions++
	fmt.Printf("Showing Landing Page %d\n", page.ID)
	return page
}

func main() {
	rand.Seed(time.Now().UnixNano())

	// Simulate 1000 visits to the site
	for i := 0; i < 1000; i++ {
		page := ShowPage()

		// Simulate button click with a 10% chance
		if rand.Float64() < 0.1 {
			ButtonClick(page)
		}
	}

	// Print the results
	for _, page := range pages {
		fmt.Printf("Landing Page %d: Impressions = %d, Clicks = %d, Conversion Rate = %.2f%%\n",
			page.ID, page.Impressions, page.Clicks, float64(page.Clicks)/float64(page.Impressions)*100)
	}
}
