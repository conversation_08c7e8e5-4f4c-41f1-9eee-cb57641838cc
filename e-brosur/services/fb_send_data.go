package services

import (
	"bytes"
	"e-brochure/models"
	setup "e-brochure/models"
	"e-brochure/repositories"
	"encoding/json"
	"io"
	"log"
	"net/http"
)

// Data structure to match the JSON body
type UserData struct {
	Email    *string `json:"em,omitempty"`
	Phone    *string `json:"ph,omitempty"`
	FirstName *string `json:"fn,omitempty"`
	LastName  *string `json:"ln,omitempty"`
	Fbclid      *string `json:"fbclid,omitempty"`
	Fbp      *string `json:"fbp,omitempty"`
	Gender    *string `json:"ge,omitempty"`
	BirthDate *string `json:"db,omitempty"`
	City      *string `json:"ct,omitempty"`
	State     *string `json:"st,omitempty"`
	Zip       *string `json:"zp,omitempty"`
	Country   *string `json:"country,omitempty"`
}
type CustomData struct {
	Currency *string  `json:"currency,omitempty"`
	Value    *float64 `json:"value,omitempty"`
	Domain *string `json:"domain,omitempty"`
	Ip       *string `json:"ip,omitempty"`
	PageURL     *string `json:"url,omitempty"`
	Ref         *string `json:"ref,omitempty"`
	VisitorID   *string `json:"visitor_id,omitempty"`
}

type Event struct {
	EventName      string     `json:"event_name,omitempty"`
	EventTime      int64      `json:"event_time,omitempty"`
	EventID        string     `json:"event_id,omitempty"`
	UserData       UserData   `json:"user_data,omitempty"`
	CustomData     CustomData `json:"custom_data,omitempty"`
	EventSourceURL string     `json:"event_source_url,omitempty"`
	ActionSource   string     `json:"action_source,omitempty"`
}



type RequestBody struct {
	Data        []Event `json:"data"`
	AccessToken string  `json:"access_token"`
}
func SendData(eventName string, eventTime int64, eventID, email, phone, firstName, lastName, gender, birthDate, city, state, zip, country, currency string, value float64, eventSourceURL, actionSource, accessToken, domain, fbp, ip, pageURL, ref, projectKey, visitorID string, pixelId string) *http.Response {
	url := "https://graph.facebook.com/v20.0/" + pixelId + "/events"
	
	// Constructing the request body using dynamic variables
	userData := UserData{}

	if email != "" {
		userData.Email = &email
	}
	if phone != "" {
		userData.Phone = hashString(phone)
	}
	
	if firstName != "" {
    	log.Println("Terjadi error atau result nil")
		userData.FirstName = hashString(firstName)
	}
	if lastName != "" {
    	log.Println("Terjadi error atau result nil")
		userData.LastName = hashString(lastName)
	}
	if gender != "" {
    	log.Println("Terjadi error atau result nil")
		userData.Gender = hashString(gender)
	}
	if birthDate != "" {
    	log.Println("Terjadi error atau result nil")
		userData.BirthDate = hashString(birthDate)
	}
	if city != "" {
    	log.Println("Terjadi error atau result nil")
		userData.City = hashString(city)
	}
	if state != "" {
    	log.Println("Terjadi error atau result nil")
		userData.State = hashString(state)
	}
	if zip != "" {
    	log.Println("Terjadi error atau result nil")
		userData.Zip = hashString(zip)
	}
	if country != "" {
    	log.Println("Terjadi error atau result nil")
		userData.Country = hashString(country)
	}
	
	CustomData := CustomData{}

	if currency != "" {
		CustomData.Currency = &currency
	}
	if value != 0 {
		CustomData.Value = &value
	}
	if domain != "" {
		CustomData.Domain = &domain
	}
	if fbp != "" {
		userData.Fbp = &fbp
	}
	if ip != "" {
		CustomData.Ip = &ip
	}
	if pageURL != "" {
		CustomData.PageURL = &pageURL
	}
	if ref != "" {
		CustomData.Ref = &ref
	}
	if visitorID != "" {
		CustomData.VisitorID = &visitorID
	}

	// Construct the request body using the pre-built userData struct
	requestBody := RequestBody{
		Data: []Event{
			{
				EventName:      eventName,
				EventTime:      eventTime,
				UserData:       userData,
				EventID:        eventID,
				CustomData:     CustomData,
				EventSourceURL: eventSourceURL,
				ActionSource:   actionSource,
			},
		},
		AccessToken: accessToken,
	}
	
	// Convert the request body to JSON
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		log.Printf("Error marshalling request body: %v", err)
		return nil
	}

	// Insert the request body to the connector log model
	connectorLog := models.ConnectorLog{
		Body: string(jsonData),
	}
	var repo repositories.ConnectorLogRepository
	if setup.InitializeDB != nil {
		repo = repositories.NewConnectorLogRepository(setup.InitializeDB())
	}

	// Use the repo instance to call the Create method
	if repo != nil {
		err := repo.Create(&connectorLog)
		if err != nil {
			log.Printf("Failed to insert request body to connector log model: %v", err)
		}
	}
	log.Println("ada repo")
	// Send the POST request
	resp, err := http.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil || resp == nil {
		return nil
	}

	// Insert the response to the connector log model
	respJSON, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("Failed to read response body: %v", err)
	}
	connectorLogNew := models.ConnectorLog{
		Body: string(respJSON),
	}
	err = repo.Create(&connectorLogNew)
	if err != nil {
		log.Printf("Failed to insert response body to connector log model: %v", err)
	}
	return resp
}

func hashString(s string) *string {
	if s == "" {
		return nil // Return nil for empty strings to avoid unnecessary hashing
	}
	return &s
}
			
