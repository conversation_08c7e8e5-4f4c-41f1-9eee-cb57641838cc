package services

import (
	"encoding/json"
	"io"
	"io/ioutil"
	"log"
	"net/http"
	"strings"
)

// ebrosur_cs_add calls the Konekwa API to add a new CS (customer service),
// passing empty values for name, phone, email, cs_name, cs_phone, and cs_webhook.
// It prints the response body to stdout.
func EbrosurCsAdd(name, phone, email, cs_name, cs_phone, cs_webhook string) (map[string]interface{}, error) {

    url := "https://api.konekwa.com/api.html"
    method := "POST"

    payload := strings.NewReader("act=ebrosur_cs_add&name=" + name + "&phone=" + phone + "&email=" + email + "&cs_name=" + cs_name + "&cs_phone=" + cs_phone + "&cs_webhook=" + cs_webhook)

    client := &http.Client{}
    req, err := http.NewRequest(method, url, payload)

    if err != nil {
        return nil, err
    }

    res, err := client.Do(req)
    if err != nil {
        return nil, err
    }
    defer res.Body.Close()

    body, err := ioutil.ReadAll(res.Body)
    if err != nil {
        return nil, err
    }

    var response map[string]interface{}
    if err := json.Unmarshal(body, &response); err != nil {
        return nil, err
    }

    return response, nil
}

func EbrosurAddMessage(phone_send,phone_to,caption,type_doc,image string) (map[string]interface{}, error) {

   url := "https://api.konekwa.com/api.html"
   method := "POST"

   log.Println("https://api.konekwa.com/api.html"+"act=ebrosur_cs_send&cs_phone=" + phone_send + "&to=" + phone_to + "&message=%7B%22caption%22%3A%22" + caption + "%22%2C%22type%22%3A%22" + type_doc + "%22%2C%20%22url%22%3A%22%" + image + "22%7D")
      payload := strings.NewReader("act=ebrosur_cs_send&cs_phone="+phone_send+"&to="+phone_to+"&message=%7B%22caption%22%3A%22"+caption+"%22%2C%22type%22%3A%22"+type_doc+"%22%2C%20%22url%22%3A%22"+image+"%22%7D")
 client := &http.Client{}
    req, err := http.NewRequest(method, url, payload)

    if err != nil {
        return nil, err
    }

    res, err := client.Do(req)
    if err != nil {
        return nil, err
    }
    defer res.Body.Close()

    body, err := io.ReadAll(res.Body)
    if err != nil {
        return nil, err
    }
    log.Println("body", string(body))
    var response map[string]interface{}
    if err := json.Unmarshal(body, &response); err != nil {
        return nil, err
    }

    return response, nil
}