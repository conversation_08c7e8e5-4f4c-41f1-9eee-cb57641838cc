package services

import (
	"fmt"
	"strings"

	"github.com/golang-jwt/jwt"
)

var jwtSecret = []byte("your_secret_key")
func GetUserIDFromToken(authHeader string) (uint, error) {
	if authHeader == "" {
		return 0, fmt.<PERSON>rrorf("authorization header is empty")
	}

	tokenString := strings.TrimPrefix(authHeader, "Bearer ")
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return jwtSecret, nil
	})

	if err != nil {
		return 0, fmt.Errorf("failed to parse token: %w", err)
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		if userID, ok := claims["userID"].(float64); ok {
			return uint(userID), nil
		}
		return 0, fmt.<PERSON><PERSON><PERSON>("userID not found in token claims")
	}

	return 0, fmt.<PERSON><PERSON><PERSON>("invalid token")
}
