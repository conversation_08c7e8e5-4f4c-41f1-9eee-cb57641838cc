package services

import (
	"fmt"
	"io/ioutil"
	"net/http"
)

func GetScreenshot(urlQuery, width, height string) ([]byte, error) {
	url := fmt.Sprintf("http://172.232.252.114:4000/ss?urlQuery=%s&width=%s&height=%s", urlQuery, width, height)
	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	return body, nil
}
